# 🎮 QuickMeet Mathematical Strategy Game - Installation Guide

Complete setup guide for QuickMeet mathematical strategy game for 2-5 players.

## 📋 Prerequisites

- **Web Server**: Apache/Nginx with PHP support
- **PHP**: Version 7.4 or higher
- **MySQL**: Version 5.7 or higher (or MariaDB 10.2+)
- **Extensions**: my<PERSON><PERSON><PERSON>, json

## 🚀 Installation Steps

### Step 1: Upload Files

Upload all game files to your web server directory:

```
QuickMeet/
├── index.php (Game homepage)
├── admin.php (Game admin dashboard)
├── create_game.php (Create game room)
├── join_game.php (Join game room)
├── game_room.php (Game interface)
├── game_handler.php (Game logic)
├── setup_game_db.php (Database setup)
├── check_game_db.php (Database checker)
├── game_setup.sql (Database schema)
├── db.php (Database connection)
└── assets/ (CSS, JS, images)
```

### Step 2: Database Configuration

1. **Edit `db.php`** with your database credentials:

```php
<?php
$servername = "localhost";
$username = "your_db_username";
$password = "your_db_password";
$dbname = "your_database_name";
?>
```

### Step 3: Database Setup

#### Option A: Automatic Setup (Recommended)

1. Open your browser and go to: `http://your-domain/setup_game_db.php`
2. Click "Setup Game Database" button
3. Wait for completion message

#### Option B: Manual Setup

1. Open phpMyAdmin or MySQL command line
2. Import the file: `game_setup.sql`
3. Verify tables were created

### Step 4: Verify Installation

1. Visit: `http://your-domain/check_game_db.php`
2. Ensure all game tables show green checkmarks
3. Test creating a game room

## 🎮 Game Features

### Mathematical Strategy Game

- **Players**: 2-5 players per game
- **Rounds**: 1-minute timed rounds
- **Objective**: Choose numbers 0-100, get closest to (average × 0.8)
- **Scoring**: Winner +1 point, others -1 point
- **Elimination**: Players eliminated at -10 points
- **Victory**: Last player standing wins

### Game Flow

1. **Create/Join**: Game rooms with unique codes
2. **Wait**: For 2-5 players to join
3. **Play**: 1-minute rounds choosing numbers
4. **Results**: See all choices and calculations
5. **Continue**: Until only one player remains

## 🔧 Admin Dashboard

### Access Admin Panel

- **URL**: `http://your-domain/admin.php`
- **Password**: `admin2550` (change in admin.php)

### Admin Features

- View game statistics
- Monitor active games
- Delete all game data
- Database management

## 🎯 Game Rules

### Round Mechanics

1. **Timer**: Each round lasts exactly 60 seconds
2. **Choice**: Players choose numbers between 0-100
3. **Auto-Submit**: Random numbers for non-responsive players
4. **Calculation**: Average of all numbers × 0.8 = target
5. **Winner**: Player closest to target gets +1 point
6. **Others**: All other players get -1 point

### Elimination System

- Players start with 0 points
- Reach -10 points = eliminated
- Last active player wins the game

### Example Round

```
3 Players submit:
Player A: 50
Player B: 60  
Player C: 70

Average: (50 + 60 + 70) ÷ 3 = 60
Target: 60 × 0.8 = 48
Winner: Player A (closest to 48)

Scores: A: +1, B: -1, C: -1
```

## 🛠️ Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check db.php credentials
   - Verify MySQL service is running

2. **Game Tables Missing**
   - Run setup_game_db.php
   - Import game_setup.sql manually

3. **Permission Errors**
   - Check file permissions (755 for directories)
   - Verify web server user access

### Database Tables

The game requires these tables:
- `game_rooms` - Game room information
- `game_players` - Player data and scores
- `game_rounds` - Number submissions
- `game_results` - Round calculations

## 📞 Support

### Team Members

- **Mohasin Shaikh** - Lead Developer
- **Dayanand Jagadale** - Backend Developer  
- **Shrinivas Solapur** - Frontend Developer
- **Saurabh Agalve** - Database Developer

### Game URLs

- **Homepage**: `index.php`
- **Create Game**: `create_game.php`
- **Join Game**: `join_game.php`
- **Admin Panel**: `admin.php`

## 🎉 Enjoy Playing!

Your QuickMeet mathematical strategy game is now ready! 

Create game rooms, invite friends, and challenge your strategic thinking skills in this exciting number-based competition.

**Good luck and have fun!** 🎮🎯🏆
