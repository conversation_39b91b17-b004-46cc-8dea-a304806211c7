<?php
include 'db.php';

// Check if room_code is provided
if (!isset($_GET['room_code']) || empty($_GET['room_code'])) {
    echo '<div style="text-align: center; padding: 2rem; color: #dc3545;">Invalid room code.</div>';
    exit;
}

$room_code = $_GET['room_code'];

// Get room information
$room_query = "SELECT * FROM rooms WHERE room_code = ?";
$room_stmt = $conn->prepare($room_query);
$room_stmt->bind_param("s", $room_code);
$room_stmt->execute();
$room_result = $room_stmt->get_result();

if ($room_result->num_rows === 0) {
    echo '<div style="text-align: center; padding: 2rem; color: #dc3545;">Room not found.</div>';
    exit;
}

$room = $room_result->fetch_assoc();

// Get room statistics
$stats_query = "
    SELECT 
        COUNT(*) as total_messages,
        COUNT(DISTINCT sender_name) as unique_participants,
        COUNT(CASE WHEN file_path IS NOT NULL THEN 1 END) as files_shared,
        MIN(timestamp) as first_message,
        MAX(timestamp) as last_message
    FROM messages 
    WHERE room_code = ?
";
$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->bind_param("s", $room_code);
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

// Get all messages for this room
$messages_query = "SELECT * FROM messages WHERE room_code = ? ORDER BY timestamp ASC";
$messages_stmt = $conn->prepare($messages_query);
$messages_stmt->bind_param("s", $room_code);
$messages_stmt->execute();
$messages_result = $messages_stmt->get_result();
?>

<style>
.room-inspection-container {
    color: var(--default-color);
}

.room-info-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(206, 175, 127, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.room-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item-modal {
    text-align: center;
    padding: 1rem;
    background: rgba(206, 175, 127, 0.1);
    border-radius: 8px;
}

.stat-number-modal {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
    display: block;
}

.stat-label-modal {
    font-size: 0.9rem;
    color: rgba(241, 243, 245, 0.7);
    margin-top: 0.25rem;
}

.messages-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(206, 175, 127, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
}

.messages-container-modal {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 1rem;
}

.message-modal {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(206, 175, 127, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.message-header-modal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.sender-name-modal {
    color: var(--accent-color);
    font-weight: 600;
}

.message-time-modal {
    color: rgba(241, 243, 245, 0.6);
    font-size: 0.85rem;
}

.message-content-modal {
    color: var(--default-color);
    margin-bottom: 0.5rem;
}

.file-attachment-modal {
    background: rgba(206, 175, 127, 0.1);
    border: 1px solid rgba(206, 175, 127, 0.3);
    border-radius: 8px;
    padding: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.file-icon-modal {
    font-size: 1.5rem;
    color: var(--accent-color);
}

.file-info-modal {
    flex: 1;
}

.file-name-modal {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.file-size-modal {
    font-size: 0.8rem;
    color: rgba(241, 243, 245, 0.7);
}

.btn-download-modal {
    background: var(--accent-color);
    color: var(--contrast-color);
    border: none;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-download-modal:hover {
    background: #d4b896;
    color: var(--contrast-color);
    text-decoration: none;
}

.no-messages {
    text-align: center;
    padding: 2rem;
    color: rgba(241, 243, 245, 0.6);
}
</style>

<div class="room-inspection-container">
    <!-- Room Information -->
    <div class="room-info-section">
        <h4 style="color: var(--accent-color); margin-bottom: 1rem;">
            <i class="bi bi-info-circle me-2"></i>Room Information
        </h4>
        
        <div class="row">
            <div class="col-md-6">
                <p><strong>Room Code:</strong> <?= htmlspecialchars($room['room_code']) ?></p>
                <p><strong>Host Name:</strong> <?= htmlspecialchars($room['name']) ?></p>
                <p><strong>Phone:</strong> <?= htmlspecialchars($room['phone']) ?></p>
            </div>
            <div class="col-md-6">
                <p><strong>Age:</strong> <?= htmlspecialchars($room['age']) ?></p>
                <p><strong>Gender:</strong> <?= htmlspecialchars($room['gender']) ?></p>
                <p><strong>Created:</strong> <?= date('M j, Y H:i', strtotime($room['created_at'])) ?></p>
            </div>
        </div>

        <!-- Room Statistics -->
        <div class="room-stats-grid">
            <div class="stat-item-modal">
                <span class="stat-number-modal"><?= $stats['total_messages'] ?></span>
                <div class="stat-label-modal">Total Messages</div>
            </div>
            <div class="stat-item-modal">
                <span class="stat-number-modal"><?= $stats['unique_participants'] ?></span>
                <div class="stat-label-modal">Participants</div>
            </div>
            <div class="stat-item-modal">
                <span class="stat-number-modal"><?= $stats['files_shared'] ?></span>
                <div class="stat-label-modal">Files Shared</div>
            </div>
            <?php if ($stats['last_message']): ?>
            <div class="stat-item-modal">
                <span class="stat-number-modal"><?= date('H:i', strtotime($stats['last_message'])) ?></span>
                <div class="stat-label-modal">Last Activity</div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Messages Section -->
    <div class="messages-section">
        <h4 style="color: var(--accent-color); margin-bottom: 1rem;">
            <i class="bi bi-chat-dots me-2"></i>Chat Messages (<?= $stats['total_messages'] ?>)
        </h4>

        <div class="messages-container-modal">
            <?php if ($messages_result->num_rows > 0): ?>
                <?php while ($msg = $messages_result->fetch_assoc()): ?>
                    <div class="message-modal">
                        <div class="message-header-modal">
                            <span class="sender-name-modal"><?= htmlspecialchars($msg['sender_name']) ?></span>
                            <span class="message-time-modal"><?= date('M j, H:i', strtotime($msg['timestamp'])) ?></span>
                        </div>
                        
                        <?php if (!empty($msg['message'])): ?>
                            <div class="message-content-modal"><?= htmlspecialchars($msg['message']) ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($msg['file_path'])): ?>
                            <?php
                            $file_size_formatted = '';
                            if ($msg['file_size']) {
                                if ($msg['file_size'] < 1024) {
                                    $file_size_formatted = $msg['file_size'] . ' B';
                                } elseif ($msg['file_size'] < 1024 * 1024) {
                                    $file_size_formatted = round($msg['file_size'] / 1024, 1) . ' KB';
                                } else {
                                    $file_size_formatted = round($msg['file_size'] / (1024 * 1024), 1) . ' MB';
                                }
                            }
                            
                            $file_icon = 'bi-file-earmark';
                            if (strpos($msg['file_type'], 'image') !== false) {
                                $file_icon = 'bi-image';
                            } elseif (strpos($msg['file_type'], 'pdf') !== false) {
                                $file_icon = 'bi-file-earmark-pdf';
                            } elseif (strpos($msg['file_type'], 'word') !== false) {
                                $file_icon = 'bi-file-earmark-word';
                            }
                            ?>
                            <div class="file-attachment-modal">
                                <i class="bi <?= $file_icon ?> file-icon-modal"></i>
                                <div class="file-info-modal">
                                    <div class="file-name-modal"><?= htmlspecialchars($msg['file_name']) ?></div>
                                    <div class="file-size-modal"><?= $file_size_formatted ?> • <?= htmlspecialchars($msg['file_type']) ?></div>
                                </div>
                                <a href="<?= htmlspecialchars($msg['file_path']) ?>" download="<?= htmlspecialchars($msg['file_name']) ?>" class="btn-download-modal">
                                    <i class="bi bi-download"></i> Download
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div class="no-messages">
                    <i class="bi bi-chat-dots" style="font-size: 3rem; color: var(--accent-color); margin-bottom: 1rem; display: block;"></i>
                    <p>No messages in this room yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
