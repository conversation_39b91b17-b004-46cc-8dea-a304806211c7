<!DOCTYPE html>
<html>
<head>
    <title>Test Break Period Fix - QuickMeet</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .test-section { border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Test Break Period Fix</h1>
    <p>This tool tests the fix for "Game not active - current status: break" error.</p>
    
    <div class="test-section">
        <h3>1. Setup Test Game</h3>
        <button onclick="setupGame()">Create & Start Game</button>
        <div id="setupResult"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Submit Numbers (Multiple Players)</h3>
        <button onclick="submitMultipleNumbers()">Submit Numbers for All Players</button>
        <div id="submitResult"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Check Game Status</h3>
        <button onclick="checkGameStatus()">Check Current Game Status</button>
        <div id="statusResult"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Test Break Period Behavior</h3>
        <button onclick="testBreakPeriod()">Test Number Submission During Break</button>
        <div id="breakResult"></div>
    </div>

    <script>
        let testRoomCode = '';
        const testPlayers = ['Player1', 'Player2', 'Player3'];
        
        async function setupGame() {
            const result = document.getElementById('setupResult');
            result.innerHTML = '<p>Setting up test game...</p>';
            
            try {
                // Create room
                const formData = new FormData();
                formData.append('action', 'create_game');
                formData.append('host_name', testPlayers[0]);
                formData.append('room_name', 'Break Test Room');
                
                const createResponse = await fetch('game_handler.php', {
                    method: 'POST',
                    body: formData
                });
                
                const createData = await JSON.parse(await createResponse.text());
                
                if (!createData.success) {
                    result.innerHTML = `<div class="error">❌ Failed to create room: ${createData.message}</div>`;
                    return;
                }
                
                testRoomCode = createData.room_code;
                result.innerHTML = `<div class="success">✅ Room created: ${testRoomCode}</div>`;
                
                // Add additional players
                for (let i = 1; i < testPlayers.length; i++) {
                    const joinData = new FormData();
                    joinData.append('action', 'join_game');
                    joinData.append('room_code', testRoomCode);
                    joinData.append('player_name', testPlayers[i]);
                    
                    await fetch('game_handler.php', {
                        method: 'POST',
                        body: joinData
                    });
                }
                
                result.innerHTML += `<div class="success">✅ Added ${testPlayers.length} players</div>`;
                
                // Start game
                const startData = new FormData();
                startData.append('action', 'start_game');
                startData.append('room_code', testRoomCode);
                startData.append('player_name', testPlayers[0]);
                
                const startResponse = await fetch('game_handler.php', {
                    method: 'POST',
                    body: startData
                });
                
                const startResult = await JSON.parse(await startResponse.text());
                
                if (startResult.success) {
                    result.innerHTML += `<div class="success">✅ Game started successfully!</div>`;
                } else {
                    result.innerHTML += `<div class="error">❌ Failed to start game: ${startResult.message}</div>`;
                }
                
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Setup error: ${error.message}</div>`;
            }
        }
        
        async function submitMultipleNumbers() {
            const result = document.getElementById('submitResult');
            result.innerHTML = '<p>Submitting numbers for all players...</p>';
            
            if (!testRoomCode) {
                result.innerHTML = '<div class="error">❌ Please setup game first</div>';
                return;
            }
            
            try {
                const numbers = [25, 50, 75]; // Different numbers for each player
                let submissions = [];
                
                for (let i = 0; i < testPlayers.length; i++) {
                    const submitData = new FormData();
                    submitData.append('action', 'submit_number');
                    submitData.append('room_code', testRoomCode);
                    submitData.append('player_name', testPlayers[i]);
                    submitData.append('chosen_number', numbers[i]);
                    
                    const response = await fetch('game_handler.php', {
                        method: 'POST',
                        body: submitData
                    });
                    
                    const data = await JSON.parse(await response.text());
                    submissions.push(`${testPlayers[i]}: ${data.success ? '✅' : '❌'} ${data.message}`);
                }
                
                result.innerHTML = `
                    <div class="info">
                        <h4>Submission Results:</h4>
                        ${submissions.map(s => `<div>${s}</div>`).join('')}
                    </div>
                `;
                
                // Wait a moment for round to complete
                setTimeout(() => {
                    result.innerHTML += '<div class="info">⏳ Waiting for round to complete and enter break period...</div>';
                }, 2000);
                
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Submit error: ${error.message}</div>`;
            }
        }
        
        async function checkGameStatus() {
            const result = document.getElementById('statusResult');
            result.innerHTML = '<p>Checking game status...</p>';
            
            if (!testRoomCode) {
                result.innerHTML = '<div class="error">❌ Please setup game first</div>';
                return;
            }
            
            try {
                const response = await fetch(`game_handler.php?action=get_game_state&room_code=${encodeURIComponent(testRoomCode)}`);
                const data = await JSON.parse(await response.text());
                
                if (data.success) {
                    result.innerHTML = `
                        <div class="success">
                            ✅ Game state retrieved successfully!<br>
                            <strong>Room Status:</strong> ${data.room?.status}<br>
                            <strong>Current Round:</strong> ${data.room?.current_round}<br>
                            <strong>Time Remaining:</strong> ${data.time_remaining || 'N/A'}s<br>
                            <strong>Break Remaining:</strong> ${data.break_remaining || 'N/A'}s<br>
                            <strong>Players:</strong> ${data.players?.length}<br>
                            <strong>Submissions:</strong> ${Object.keys(data.submissions || {}).length}
                        </div>
                    `;
                    
                    if (data.room?.status === 'break') {
                        result.innerHTML += '<div class="info">🎯 Game is in BREAK period - perfect for testing!</div>';
                    } else if (data.room?.status === 'playing') {
                        result.innerHTML += '<div class="info">🎮 Game is PLAYING - submit numbers to trigger break</div>';
                    }
                } else {
                    result.innerHTML = `<div class="error">❌ Failed to get game state: ${data.message}</div>`;
                }
                
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Status check error: ${error.message}</div>`;
            }
        }
        
        async function testBreakPeriod() {
            const result = document.getElementById('breakResult');
            result.innerHTML = '<p>Testing number submission during break period...</p>';
            
            if (!testRoomCode) {
                result.innerHTML = '<div class="error">❌ Please setup game first</div>';
                return;
            }
            
            try {
                // Try to submit a number during break period
                const submitData = new FormData();
                submitData.append('action', 'submit_number');
                submitData.append('room_code', testRoomCode);
                submitData.append('player_name', testPlayers[0]);
                submitData.append('chosen_number', '99');
                
                const response = await fetch('game_handler.php', {
                    method: 'POST',
                    body: submitData
                });
                
                const data = await JSON.parse(await response.text());
                
                if (data.success) {
                    result.innerHTML = `<div class="error">❌ Unexpected: Number submission succeeded during break period!</div>`;
                } else {
                    if (data.message.includes('break') || data.message.includes('showing results')) {
                        result.innerHTML = `
                            <div class="success">
                                ✅ Break period handling works correctly!<br>
                                <strong>Error Message:</strong> ${data.message}<br>
                                <em>This is the expected behavior during break periods.</em>
                            </div>
                        `;
                    } else {
                        result.innerHTML = `
                            <div class="info">
                                ℹ️ Different error received:<br>
                                <strong>Message:</strong> ${data.message}<br>
                                <em>Game might not be in break period yet.</em>
                            </div>
                        `;
                    }
                }
                
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Test error: ${error.message}</div>`;
            }
        }
    </script>
    
    <div class="info">
        <h3>📋 Test Instructions:</h3>
        <ol>
            <li><strong>Setup:</strong> Create a test game with multiple players</li>
            <li><strong>Submit:</strong> Submit numbers for all players to complete a round</li>
            <li><strong>Check:</strong> Verify the game enters "break" status</li>
            <li><strong>Test:</strong> Try to submit a number during break period</li>
        </ol>
        
        <h3>✅ Expected Results:</h3>
        <ul>
            <li>Game should enter "break" status after all players submit</li>
            <li>Number submission during break should be rejected with friendly message</li>
            <li>No more "Fatal error" or "Connection error" messages</li>
            <li>Game should automatically progress to next round after break</li>
        </ul>
    </div>
</body>
</html>
