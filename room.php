<?php include 'db.php'; ?>
<?php
$room_code = $_GET['code'] ?? '';
$name = $_GET['name'] ?? '';

// Fetch room password for WhatsApp message
$password = '';
$res = $conn->query("SELECT password FROM rooms WHERE room_code = '$room_code' LIMIT 1");
if ($res && $res->num_rows > 0) {
    $row = $res->fetch_assoc();
    $password = $row['password'];
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Room <?= htmlspecialchars($room_code) ?> - QuickMeet</title>
  <meta name="description" content="Chat room for instant group communication">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    /* Custom styles for the chat room, adapted from the original room.php */
    :root {
        --default-color: #f1f3f5;
        --heading-color: #d7e0e7;
        --accent-color: #ceaf7f; /* A warm, inviting gold/brown */
        --surface-color: #1a1a1a; /* Dark background for sections */
        --contrast-color: #0d0d0d; /* Even darker for strong contrast */
        --border-color: rgba(206, 175, 127, 0.1); /* Subtle border */
    }

    body {
        font-family: "Poppins", sans-serif;
        color: var(--default-color);
        background: #0a0a0a; /* A very dark background */
        overflow-x: hidden;
    }

    .chat-container {
      min-height: 100vh;
      padding: 120px 0 20px 0;
    }

    .room-header {
      background: var(--surface-color);
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--border-color);
    }

    .room-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .room-actions-container {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .room-details h2 {
      color: var(--heading-color);
      margin: 0;
      font-size: 1.5rem;
    }

    .room-code {
      background: var(--accent-color);
      color: var(--contrast-color);
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 1.1rem;
      letter-spacing: 0.1em;
    }

    .chat-section {
      background: var(--surface-color);
      border-radius: 15px;
      border: 1px solid var(--border-color);
      overflow: hidden;
      margin-bottom: 2rem;
      display: flex; /* Use flexbox for chat section layout */
      flex-direction: column; /* Stack chatbox and form vertically */
    }

    .chat-header {
      background: rgba(206, 175, 127, 0.1);
      padding: 1rem 1.5rem;
      border-bottom: 1px solid var(--border-color);
    }

    .chat-header h4 {
      margin: 0;
      color: var(--heading-color);
      display: flex;
      align-items: center;
    }

    /* Updated chatBox styles for AJAX content */
    #chatBox {
      height: 500px; /* Fixed height for scrollability */
      overflow-y: auto; /* Enable vertical scrolling */
      padding: 1.5rem;
      background: transparent;
      flex-grow: 1; /* Allow it to grow and take available space */
      /* These next three lines are from your original request for how messages should look */
      border: 1px solid #ccc;
      background: #f9f9f9;
      color: #333; /* Dark text for light background */
    }

    /* Original message styles from your initial request */
    .message {
      margin-bottom: 10px;
    }
    .sender {
      font-weight: bold;
    }
    .file-link {
      color: blue;
      text-decoration: underline;
    }


    .message-form {
      padding: 1.5rem;
      background: rgba(0, 0, 0, 0.1);
      border-top: 1px solid var(--border-color); /* Separator from chat box */
    }

    .message-input-group {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .message-input {
      flex: 1;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 25px;
      padding: 12px 20px;
      color: var(--default-color);
      font-size: 1rem;
    }

    .message-input:focus {
      background: rgba(255, 255, 255, 0.1);
      border-color: var(--accent-color);
      box-shadow: 0 0 0 0.2rem rgba(206, 175, 127, 0.25);
      color: var(--default-color);
      outline: none;
    }

    .message-input::placeholder {
      color: rgba(241, 243, 245, 0.6);
    }

    .btn-send, .btn-file {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      border: none;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
      transition: all 0.3s ease;
      margin-left: 0.5rem;
    }

    .btn-file {
      background: linear-gradient(135deg, #6c757d, #5a6268); /* Grey gradient for file button */
    }

    .btn-send:hover, .btn-file:hover {
      transform: scale(1.1);
      box-shadow: 0 5px 15px rgba(206, 175, 127, 0.3);
    }

    .btn-file:hover {
      box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
    }

    .file-preview {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 12px;
      padding: 1rem;
      margin-top: 1rem;
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .file-preview-info {
      flex: 1;
    }

    .file-preview-name {
      color: var(--default-color);
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    .file-preview-size {
      color: rgba(241, 243, 245, 0.7);
      font-size: 0.9rem;
    }

    .file-preview-remove {
      background: #dc3545; /* Red for remove button */
      color: white;
      border: none;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .file-preview-remove:hover {
      background: #c82333;
      transform: scale(1.1);
    }

    .share-section {
      background: var(--surface-color);
      border-radius: 15px;
      padding: 2rem;
      border: 1px solid var(--border-color);
    }

    .share-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .share-header h4 {
      color: var(--heading-color);
      margin-bottom: 0.5rem;
    }

    .share-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 2rem;
    }

    .btn-copy, .btn-whatsapp {
      padding: 12px 24px;
      border-radius: 25px;
      border: none;
      font-weight: 500;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-copy {
      background: rgba(206, 175, 127, 0.2);
      color: var(--accent-color);
      border: 1px solid var(--accent-color);
    }

    .btn-copy:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .btn-whatsapp {
      background: #25D366; /* WhatsApp green */
      color: white;
    }

    .btn-whatsapp:hover {
      background: #128C7E;
      transform: translateY(-2px);
    }

    .whatsapp-form {
      max-width: 400px;
      margin: 0 auto;
    }

    .whatsapp-input {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 10px;
      padding: 12px 16px;
      color: var(--default-color);
      width: 100%;
      margin-bottom: 1rem;
    }

    .whatsapp-input:focus {
      background: rgba(255, 255, 255, 0.1);
      border-color: var(--accent-color);
      box-shadow: 0 0 0 0.2rem rgba(206, 175, 127, 0.25);
      color: var(--default-color);
      outline: none;
    }

    .whatsapp-input::placeholder {
      color: rgba(241, 243, 245, 0.6);
    }

    .back-link {
      color: var(--accent-color);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      margin-bottom: 2rem;
      transition: color 0.3s ease;
    }

    .back-link:hover {
      color: #d4b896;
    }

    .back-link i {
      margin-right: 0.5rem;
    }

    /* Styles for the menu toggle button */
    .menu-toggle-btn {
      background: var(--surface-color);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 50%;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--accent-color);
      font-size: 1.5rem;
      transition: all 0.3s ease;
      cursor: pointer;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .menu-toggle-btn:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(206, 175, 127, 0.3);
    }

    .menu-toggle-btn:active {
      transform: translateY(0);
    }

    .close-menu-btn {
      background: transparent;
      border: none;
      color: var(--default-color);
      font-size: 1.2rem;
      padding: 5px;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .close-menu-btn:hover {
      background: rgba(220, 53, 69, 0.2); /* Light red on hover */
      color: #ff6b7a; /* Brighter red text */
    }

    .share-section {
      animation: slideIn 0.3s ease;
      transform-origin: top;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    /* Notification styles */
    .notification {
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--accent-color);
        color: var(--contrast-color);
        padding: 1rem 1.5rem;
        border-radius: 10px;
        z-index: 9999;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        animation: slideInNotification 0.3s ease-out;
    }

    @keyframes slideInNotification {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

  </style>
</head>

<body>
  <!-- Header -->
  <header id="header" class="header d-flex align-items-center sticky-top">
    <div class="container position-relative d-flex align-items-center justify-content-between">

      <a href="index.php" class="logo d-flex align-items-center me-auto me-xl-0">
        <img src="assets/img/chatbot-logo.png" alt="QuickMeet Logo" style="height: 40px; margin-right: 0.5rem;">
        <h1 class="sitename">QuickMeet</h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.php">Home</a></li>
          <li><a href="index.php#features">Features</a></li>
          <li><a href="index.php#how-it-works">How It Works</a></li>
          <li><a href="https://quickf.free.nf/" target="_blank">Quick</a></li>
          <li><a href="admin.php">Admin</a></li>
          <li><a href="create_room.php">Create Room</a></li>
          <li><a href="join_room.php">Join Room</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

    </div>
  </header>

  <main class="main">
    <section class="chat-container">
      <div class="container" data-aos="fade-up">
        <a href="index.php" class="back-link">
          <i class="bi bi-arrow-left"></i>Back to Home
        </a>

        <!-- Room Header -->
        <div class="room-header">
          <div class="room-info">
            <div class="room-details">
              <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem;"><i class="bi bi-people me-3"></i>Welcome <?= htmlspecialchars($name) ?>!</h1>
              <p class="mb-0" style="font-size: 1.2rem; color: var(--default-color);">You're now in the chat room</p>
            </div>
            <div class="room-actions-container">
              <div class="room-code" style="font-size: 1.3rem; padding: 1rem 1.5rem;">
                <i class="bi bi-key me-2"></i>Room: <?= htmlspecialchars($room_code) ?>
              </div>
              <!-- Menu Button -->
              <button type="button" class="menu-toggle-btn" onclick="toggleInviteMenu()" style="margin-left: 1rem;">
                <i class="bi bi-three-dots-vertical"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Share Section (Hidden by default) -->
        <div class="share-section" id="inviteMenu" style="display: none; margin-bottom: 2rem;">
          <div class="share-header">
            <div class="d-flex justify-content-between align-items-center">
              <h4><i class="bi bi-share me-2"></i>Invite Friends</h4>
              <button type="button" class="close-menu-btn" onclick="toggleInviteMenu()">
                <i class="bi bi-x"></i>
              </button>
            </div>
            <p class="text-muted">Share this room with others</p>
          </div>

          <div class="share-buttons">
            <button type="button" class="btn-copy" onclick="copyCode()">
              <i class="bi bi-clipboard"></i>Copy Code
            </button>
            <button type="button" class="btn-copy" onclick="copyLink()">
              <i class="bi bi-link-45deg"></i>Copy Link
            </button>
          </div>

          <div class="whatsapp-form">
            <h6 class="text-center mb-3"><i class="bi bi-whatsapp me-2"></i>Send via WhatsApp</h6>
            <form id="whatsappForm" onsubmit="sendWhatsApp(); return false;">
              <input type="text" id="whatsappNumber" class="whatsapp-input"
                     placeholder="Enter WhatsApp number (e.g., +1234567890)" required>
              <button type="submit" class="btn-whatsapp w-100">
                <i class="bi bi-whatsapp me-2"></i>Send Invitation
              </button>
            </form>
          </div>
        </div>

        <!-- Chat Section (Full Width) -->
        <div class="chat-section">
          <div class="chat-header">
            <h4><i class="bi bi-chat-dots me-2"></i>Live Chat</h4>
          </div>
          <!-- The chat box content will now be loaded directly here via AJAX -->
          <div id="chatBox">
            <!-- Messages will be dynamically loaded here -->
          </div>
          <div class="message-form">
            <form method="post" id="messageForm">
              <div class="message-input-group">
                <input type="text" name="message" class="message-input"
                       placeholder="Type your message..." autocomplete="off">
                <input type="file" id="fileInput" name="file" style="display: none;"
                       accept="image/*,.pdf,.doc,.docx,.txt,.zip,.rar">
                <button type="button" class="btn-file" onclick="document.getElementById('fileInput').click()">
                  <i class="bi bi-paperclip"></i>
                </button>
                <button type="submit" class="btn-send">
                  <i class="bi bi-send"></i>
                </button>
              </div>
              <div id="filePreview" class="file-preview" style="display: none;"></div>
            </form>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer id="footer" class="footer dark-background">
    <div class="container">
      <div class="row gy-3">
        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-geo-alt icon"></i>
          <div class="address">
            <h4>Address</h4>
            <p>QuickMeet HQ<br>Digital Communication Center<br></p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-telephone icon"></i>
          <div>
            <h4>Contact</h4>
            <p>
              <strong>Phone:</strong> <span>+91 7028844513</span><br>
              <strong>Email:</strong> <span><EMAIL></span><br>
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-clock icon"></i>
          <div>
            <h4>Available</h4>
            <p>
              <strong>24/7 Service</strong><br>
              Always online and ready to connect you with friends and family.
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6">
          <h4>Follow Us</h4>
          <div class="social-links d-flex">
            <a href="#" class="twitter"><i class="bi bi-twitter-x"></i></a>
            <a href="#" class="facebook"><i class="bi bi-facebook"></i></a>
            <a href="#" class="instagram"><i class="bi bi-instagram"></i></a>
            <a href="#" class="linkedin"><i class="bi bi-linkedin"></i></a>
          </div>
        </div>
      </div>
    </div>

    <div class="container copyright text-center mt-4">
      <p><span>©</span> <strong class="px-1 sitename">QuickMeet</strong> <span>All Rights Reserved</span></p>
    </div>
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <script>
    // Global variables
    const chatBox = document.getElementById('chatBox');
    const messageForm = document.getElementById('messageForm');
    const messageInput = document.querySelector('input[name="message"]');
    const fileInput = document.getElementById('fileInput');
    let selectedFile = null;

    // Auto-focus message input
    messageInput.focus();

    // Function to scroll chatbox to bottom
    function scrollToBottom() {
        chatBox.scrollTop = chatBox.scrollHeight;
    }

    // Load messages via AJAX (reverted to original text fetch logic)
    async function loadMessages() {
        try {
            const res = await fetch('load_messages.php?code=<?= urlencode($room_code) ?>');
            const html = await res.text();
            chatBox.innerHTML = html;
            scrollToBottom();
        } catch (error) {
            console.error('Error loading messages:', error);
        }
    }

    // Handle Enter key in message input
    messageInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault(); // Prevent default form submission
        messageForm.dispatchEvent(new Event('submit', { cancelable: true }));
      }
    });

    // Copy room code function
    function copyCode() {
      const code = "<?= $room_code ?>";
      navigator.clipboard.writeText(code).then(() => {
        showNotification("Room code copied: " + code);
      }).catch(() => {
        const textArea = document.createElement("textarea");
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification("Room code copied: " + code);
      });
    }

    // Copy join link function
    function copyLink() {
      const link = window.location.origin + "/join_room.php";
      navigator.clipboard.writeText(link).then(() => {
        showNotification("Join link copied!");
      }).catch(() => {
        const textArea = document.createElement("textarea");
        textArea.value = link;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification("Join link copied!");
      });
    }

    // Send WhatsApp invitation
    function sendWhatsApp() {
      const number = document.getElementById('whatsappNumber').value;
      const code = "<?= $room_code ?>";
      const password = "<?= $password ?>";
      const link = window.location.origin + "/join_room.php";
      const text = encodeURIComponent(`👋 You're invited to join my QuickMeet room!

🔗 Room Code: ${code}
🔒 Password: ${password}
➡ Join here: ${link}

See you in the chat! 💬`);
      const url = https://wa.me/${number}?text=${text};
      window.open(url, '_blank');
    }

    // Show notification function
    function showNotification(message) {
      const notification = document.createElement('div');
      notification.classList.add('notification');
      notification.textContent = message;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Toggle invite menu function
    function toggleInviteMenu() {
      const menu = document.getElementById('inviteMenu');
      const button = document.querySelector('.menu-toggle-btn');

      if (menu.style.display === 'none' || menu.style.display === '') {
        menu.style.display = 'block';
        button.innerHTML = '<i class="bi bi-x-lg"></i>';

        setTimeout(() => {
          document.addEventListener('click', closeMenuOnClickOutside);
        }, 100);
      } else {
        menu.style.display = 'none';
        button.innerHTML = '<i class="bi bi-three-dots-vertical"></i>';
        document.removeEventListener('click', closeMenuOnClickOutside);
      }
    }

    // Close menu when clicking outside
    function closeMenuOnClickOutside(event) {
      const menu = document.getElementById('inviteMenu');
      const button = document.querySelector('.menu-toggle-btn');

      if (!menu.contains(event.target) && !button.contains(event.target)) {
        menu.style.display = 'none';
        button.innerHTML = '<i class="bi bi-three-dots-vertical"></i>';
        document.removeEventListener('click', closeMenuOnClickOutside);
      }
    }

    // File upload functionality
    fileInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        selectedFile = file;
        showFilePreview(file);
      }
    });

    function showFilePreview(file) {
      const preview = document.getElementById('filePreview');
      const fileName = file.name;
      const fileSize = formatFileSize(file.size);

      preview.innerHTML = `
        <i class="bi bi-file-earmark file-icon" style="font-size: 2rem; color: var(--accent-color);"></i>
        <div class="file-preview-info">
          <div class="file-preview-name">${fileName}</div>
          <div class="file-preview-size">${fileSize}</div>
        </div>
        <button type="button" class="file-preview-remove" onclick="removeFilePreview()">
          <i class="bi bi-x"></i>
        </button>
      `;
      preview.style.display = 'flex';
    }

    function removeFilePreview() {
      document.getElementById('filePreview').style.display = 'none';
      fileInput.value = ''; // Clear the file input
      selectedFile = null;
    }

    function formatFileSize(bytes) {
      if (bytes < 1024) return bytes + ' B';
      if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }

    // Handle form submission for messages and files via AJAX
    messageForm.addEventListener('submit', async function(e) {
      e.preventDefault();

      const message = messageInput.value.trim();

      if (!message && !selectedFile) {
        showNotification('Please enter a message or select a file to share.');
        return;
      }

      const formData = new FormData();
      formData.append('room_code', '<?= htmlspecialchars($room_code) ?>');
      formData.append('sender_name', '<?= htmlspecialchars($name) ?>');
      formData.append('message', message);

      if (selectedFile) {
        formData.append('file', selectedFile);
        try {
            const response = await fetch('upload_file.php', {
                method: 'POST',
                body: formData
            });
            const data = await response.json();
            if (!data.success) {
                showNotification('Error uploading file: ' + data.message);
                return;
            }
        } catch (error) {
            console.error('Upload error:', error);
            showNotification('Error uploading file. Please try again.');
            return; // Exit if upload fails
        }
      } else {
        try {
            // Send text message only
            await fetch('send_message.php?code=<?= urlencode($room_code) ?>&name=<?= urlencode($name) ?>', {
                method: 'POST',
                body: formData
            });
        } catch (error) {
            console.error('Message send error:', error);
            showNotification('Error sending message. Please try again.');
            return; // Exit if send fails
        }
      }

      // Clear form and refresh messages after successful send/upload
      messageInput.value = '';
      removeFilePreview();
      loadMessages(); // Reload messages to show the new one
    });

    // Initialize AOS (Animate On Scroll) library
    AOS.init();

    // Initial load of messages
    loadMessages();

    // Auto-refresh messages every 2 seconds
    setInterval(loadMessages, 2000);
  </script>

</body>
</html>