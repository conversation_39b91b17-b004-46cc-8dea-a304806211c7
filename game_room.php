<?php include 'db.php'; ?>
<?php
$room_code = $_GET['code'] ?? '';
$player_name = $_GET['name'] ?? '';

if (empty($room_code) || empty($player_name)) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Game Room <?php echo htmlspecialchars($room_code); ?> - QuickMeet</title>
  <meta name="description" content="QuickMeet Mathematical Strategy Game Room">
  <meta name="keywords" content="QuickMeet, math game, strategy game, multiplayer">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    body {
      font-family: var(--default-font);
      color: var(--default-color);
      background-color: var(--background-color);
      position: relative;
      margin: 0;
      padding: 0;
      height: 100vh;
      overflow: hidden;
    }

    body::before {
      background: linear-gradient(color-mix(in srgb, #000000, transparent 30%), color-mix(in srgb, #000000, transparent 30%)), url("assets/img/bg/abstract-bg-4.webp") no-repeat center center/cover;
      content: "";
      position: fixed;
      inset: 0;
      z-index: 0;
      pointer-events: none;
    }

    .game-container {
      display: flex;
      height: 100vh;
      position: relative;
      z-index: 1;
    }

    /* Game Area (Left Side) */
    .game-area {
      flex: 1;
      background: var(--surface-color);
      border-right: 1px solid rgba(206, 175, 127, 0.2);
      padding: 2rem;
      overflow-y: auto;
    }

    .game-header {
      text-align: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid rgba(206, 175, 127, 0.2);
    }

    .room-title {
      color: var(--heading-color);
      font-size: 2rem;
      font-weight: 700;
      margin: 0;
      font-family: var(--heading-font);
    }

    .game-status {
      color: var(--accent-color);
      font-size: 1.2rem;
      margin-top: 0.5rem;
    }

    .timer-display {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 1rem 2rem;
      border-radius: 15px;
      font-size: 2rem;
      font-weight: 700;
      text-align: center;
      margin: 1rem 0;
    }

    .players-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .player-card {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 12px;
      padding: 1rem;
      text-align: center;
    }

    .player-card.current-player {
      border-color: var(--accent-color);
      background: rgba(206, 175, 127, 0.1);
    }

    .player-card.eliminated {
      opacity: 0.5;
      background: rgba(220, 53, 69, 0.1);
      border-color: #dc3545;
    }

    .player-name {
      color: var(--heading-color);
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .player-score {
      color: var(--accent-color);
      font-size: 1.5rem;
      font-weight: 700;
    }

    .player-status {
      font-size: 0.9rem;
      margin-top: 0.5rem;
    }

    .number-input-section {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 15px;
      padding: 2rem;
      text-align: center;
      margin-bottom: 2rem;
    }

    .number-input {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(206, 175, 127, 0.2);
      border-radius: 12px;
      padding: 1rem 2rem;
      font-size: 2rem;
      color: var(--default-color);
      text-align: center;
      width: 200px;
      margin: 1rem;
    }

    .number-input:focus {
      outline: none;
      border-color: var(--accent-color);
      background: rgba(255, 255, 255, 0.15);
    }

    .submit-btn {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 1rem 3rem;
      border-radius: 12px;
      font-weight: 600;
      font-size: 1.2rem;
      border: none;
      cursor: pointer;
      margin: 1rem;
    }

    .submit-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .round-results {
      background: rgba(206, 175, 127, 0.1);
      border: 1px solid var(--accent-color);
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1rem;
    }

    /* Chat Area (Right Side) */
    .chat-area {
      width: 400px;
      background: var(--surface-color);
      display: flex;
      flex-direction: column;
      border-left: 1px solid rgba(206, 175, 127, 0.2);
    }

    .chat-header {
      background: rgba(206, 175, 127, 0.1);
      padding: 1rem;
      border-bottom: 1px solid rgba(206, 175, 127, 0.2);
      text-align: center;
    }

    .chat-toggle {
      background: var(--accent-color);
      color: var(--contrast-color);
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      cursor: pointer;
      font-size: 0.9rem;
    }

    #chatBox {
      flex: 1;
      background: rgba(0, 0, 0, 0.2);
      padding: 1rem;
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: var(--accent-color) transparent;
    }

    #chatBox::-webkit-scrollbar {
      width: 6px;
    }

    #chatBox::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }

    #chatBox::-webkit-scrollbar-thumb {
      background: var(--accent-color);
      border-radius: 3px;
    }

    .message {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 0.75rem;
      margin-bottom: 0.5rem;
      border-left: 3px solid var(--accent-color);
    }

    .message.system {
      background: rgba(206, 175, 127, 0.1);
      border-left-color: var(--accent-color);
    }

    .sender {
      color: var(--accent-color);
      font-weight: 600;
      font-size: 0.9rem;
      margin-bottom: 0.25rem;
    }

    .chat-form {
      background: rgba(255, 255, 255, 0.05);
      padding: 1rem;
      border-top: 1px solid rgba(206, 175, 127, 0.2);
    }

    .chat-input {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 8px;
      padding: 0.75rem;
      color: var(--default-color);
      width: 100%;
      margin-bottom: 0.5rem;
    }

    .chat-input:focus {
      outline: none;
      border-color: var(--accent-color);
    }

    .chat-send-btn {
      background: var(--accent-color);
      color: var(--contrast-color);
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      cursor: pointer;
      width: 100%;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .game-container {
        flex-direction: column;
      }
      
      .chat-area {
        width: 100%;
        height: 300px;
      }
      
      .game-area {
        padding: 1rem;
      }
    }

    /* Waiting room styles */
    .waiting-room {
      text-align: center;
      padding: 3rem;
    }

    .start-game-btn {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 1rem 3rem;
      border-radius: 12px;
      font-weight: 600;
      font-size: 1.2rem;
      border: none;
      cursor: pointer;
      margin: 2rem;
    }
  </style>
</head>

<body>
  <div class="game-container">
    <!-- Game Area (Left Side) -->
    <div class="game-area">
      <div class="game-header">
        <h1 class="room-title">🎮 Room <?php echo htmlspecialchars($room_code); ?></h1>
        <div class="game-status" id="gameStatus">Loading...</div>
      </div>

      <!-- Game Content -->
      <div id="gameContent">
        <!-- Content will be loaded dynamically -->
      </div>
    </div>

    <!-- Chat Area (Right Side) -->
    <div class="chat-area">
      <div class="chat-header">
        <h4 style="color: var(--heading-color); margin: 0;">💬 Game Chat</h4>
      </div>
      
      <div id="chatBox"></div>
      
      <form id="chatForm" method="POST" enctype="multipart/form-data" class="chat-form">
        <input type="hidden" name="action" value="send">
        <input type="text" name="message" class="chat-input" placeholder="Type your message..." required>
        <button type="submit" class="chat-send-btn">Send</button>
      </form>
    </div>
  </div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <script>
    const roomCode = '<?php echo htmlspecialchars($room_code); ?>';
    const playerName = '<?php echo htmlspecialchars($player_name); ?>';
    let gameState = null;
    let playerId = localStorage.getItem(`player_id_${roomCode}`) || '';
    let currentInputValue = ''; // Store current input value
    let isSubmitting = false; // Prevent double submission

    // Chat functionality
    const chatBox = document.getElementById('chatBox');
    const chatForm = document.getElementById('chatForm');

    async function loadMessages() {
      try {
        const res = await fetch(`load_messages.php?code=${encodeURIComponent(roomCode)}`);
        const html = await res.text();
        chatBox.innerHTML = html;
        chatBox.scrollTop = chatBox.scrollHeight;
      } catch (error) {
        console.error('Error loading messages:', error);
      }
    }

    chatForm.onsubmit = async function (e) {
      e.preventDefault();
      const formData = new FormData(chatForm);
      
      try {
        const res = await fetch(`send_message.php?code=${encodeURIComponent(roomCode)}&name=${encodeURIComponent(playerName)}`, {
          method: 'POST',
          body: formData
        });
        chatForm.reset();
        loadMessages();
      } catch (error) {
        console.error('Error sending message:', error);
      }
    };

    // Game functionality
    async function loadGameState() {
      try {
        const res = await fetch(`game_handler.php?action=get_game_state&room_code=${encodeURIComponent(roomCode)}`);
        const data = await res.json();
        
        if (data.success) {
          gameState = data;
          updateGameDisplay();
        } else {
          document.getElementById('gameStatus').textContent = 'Error: ' + data.message;
        }
      } catch (error) {
        console.error('Error loading game state:', error);
        document.getElementById('gameStatus').textContent = 'Connection error';
      }
    }

    function updateGameDisplay() {
      const status = document.getElementById('gameStatus');
      const content = document.getElementById('gameContent');

      if (!gameState) return;

      // Preserve current input value before updating
      const numberInput = document.getElementById('numberInput');
      if (numberInput && !numberInput.disabled) {
        currentInputValue = numberInput.value;
      }

      const room = gameState.room;
      const players = gameState.players;

      if (room.status === 'waiting') {
        status.textContent = `Waiting for players (${players.length}/5)`;
        content.innerHTML = renderWaitingRoom();
      } else if (room.status === 'playing') {
        status.textContent = `Round ${room.current_round} - ${gameState.time_remaining}s remaining`;
        content.innerHTML = renderGamePlay();

        // Restore input value after rendering
        setTimeout(() => {
          const newNumberInput = document.getElementById('numberInput');
          if (newNumberInput && currentInputValue) {
            newNumberInput.value = currentInputValue;
          }
        }, 10);
      } else if (room.status === 'finished') {
        status.textContent = 'Game Finished!';
        content.innerHTML = renderGameFinished();
      }
    }

    function renderWaitingRoom() {
      const players = gameState.players;

      let html = '<div class="waiting-room">';
      html += '<h3 style="color: var(--heading-color);">Waiting for Players</h3>';
      html += '<div class="players-grid">';

      players.forEach(player => {
        html += `<div class="player-card">
          <div class="player-name">${player.player_name}</div>
          <div class="player-status">${player.player_name === gameState.room.host_name ? '👑 Host' : '👤 Player'}</div>
        </div>`;
      });

      // Add empty slots
      for (let i = players.length; i < 5; i++) {
        html += `<div class="player-card" style="opacity: 0.3;">
          <div class="player-name">Waiting...</div>
          <div class="player-status">Empty Slot</div>
        </div>`;
      }

      html += '</div>';

      // Any player can start the game if there are at least 2 players
      if (players.length >= 2) {
        html += `<button class="start-game-btn" onclick="startGame()">
          <i class="bi bi-play-circle me-2"></i>Start Game (${players.length}/5 players)
        </button>`;
        html += '<p style="color: var(--default-color); margin-top: 1rem; font-size: 0.9rem;">Any player can start the game when ready!</p>';
      } else {
        html += '<p style="color: var(--accent-color); margin-top: 2rem; font-size: 1.1rem;">Waiting for more players... (Need at least 2 to start)</p>';
      }

      html += '</div>';
      return html;
    }

    function renderGamePlay() {
      let html = '';
      
      // Timer
      html += `<div class="timer-display">⏰ ${gameState.time_remaining}s</div>`;
      
      // Players grid
      html += '<div class="players-grid">';
      gameState.players.forEach(player => {
        const isCurrentPlayer = player.player_name === playerName;
        const hasSubmitted = gameState.submissions[player.player_name] || false;
        
        html += `<div class="player-card ${isCurrentPlayer ? 'current-player' : ''} ${player.status === 'eliminated' ? 'eliminated' : ''}">
          <div class="player-name">${player.player_name}</div>
          <div class="player-score">${player.score} pts</div>
          <div class="player-status">
            ${player.status === 'eliminated' ? '💀 Eliminated' : 
              hasSubmitted ? '✅ Submitted' : '⏳ Thinking...'}
          </div>
        </div>`;
      });
      html += '</div>';
      
      // Number input (only for active players)
      const currentPlayer = gameState.players.find(p => p.player_name === playerName);
      if (currentPlayer && currentPlayer.status === 'active') {
        const hasSubmitted = gameState.submissions[playerName] || false;

        html += '<div class="number-input-section">';
        html += '<h4 style="color: var(--heading-color);">Choose Your Number (0-100)</h4>';

        if (hasSubmitted) {
          html += `<div class="submitted-number">
            <div style="font-size: 2rem; color: var(--accent-color); margin: 1rem;">✅ Number Submitted!</div>
            <div style="color: var(--default-color);">Waiting for other players...</div>
          </div>`;
        } else {
          html += `<input type="number" id="numberInput" class="number-input" min="0" max="100"
                   placeholder="Enter 0-100" value="${currentInputValue}">`;
          html += `<br><button class="submit-btn" onclick="submitNumber()" id="submitBtn">
                   🎯 Submit Number
                   </button>`;
        }
        html += '</div>';
      }
      
      // Last round results
      if (gameState.last_result) {
        const result = gameState.last_result;
        html += `<div class="round-results">
          <h4 style="color: var(--accent-color);">📊 Previous Round Results</h4>
          <p><strong>Average:</strong> ${parseFloat(result.average_number).toFixed(2)}</p>
          <p><strong>Target (×0.8):</strong> ${parseFloat(result.target_number).toFixed(2)}</p>
          <p><strong>Winner:</strong> 🏆 ${result.winner_name}</p>
        </div>`;
      }
      
      return html;
    }

    function renderGameFinished() {
      const winner = gameState.players.find(p => p.status === 'active');
      
      let html = '<div class="waiting-room">';
      html += '<h2 style="color: var(--accent-color);">🎉 Game Over!</h2>';
      
      if (winner) {
        html += `<h3 style="color: var(--heading-color);">🏆 Winner: ${winner.player_name}</h3>`;
      }
      
      html += '<h4 style="color: var(--heading-color);">Final Scores:</h4>';
      html += '<div class="players-grid">';
      
      // Sort players by score
      const sortedPlayers = [...gameState.players].sort((a, b) => b.score - a.score);
      
      sortedPlayers.forEach((player, index) => {
        html += `<div class="player-card ${player.status === 'eliminated' ? 'eliminated' : ''}">
          <div class="player-name">${index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅'} ${player.player_name}</div>
          <div class="player-score">${player.score} pts</div>
          <div class="player-status">${player.status === 'eliminated' ? 'Eliminated' : 'Survived'}</div>
        </div>`;
      });
      
      html += '</div>';
      html += '<a href="index.php" class="start-game-btn">🏠 Back to Home</a>';
      html += '</div>';
      
      return html;
    }

    async function startGame() {
      try {
        const formData = new FormData();
        formData.append('action', 'start_game');
        formData.append('room_code', roomCode);
        formData.append('player_name', playerName);

        const res = await fetch('game_handler.php', {
          method: 'POST',
          body: formData
        });

        const data = await res.json();
        if (!data.success) {
          alert('Error: ' + data.message);
        }
      } catch (error) {
        console.error('Error starting game:', error);
        alert('Failed to start game');
      }
    }

    async function submitNumber() {
      if (isSubmitting) {
        return; // Prevent double submission
      }

      const numberInput = document.getElementById('numberInput');
      const submitBtn = document.getElementById('submitBtn');

      if (!numberInput) {
        alert('Input field not found. Please try again.');
        return;
      }

      const inputValue = numberInput.value.trim();
      const number = parseInt(inputValue);

      // Validation
      if (inputValue === '' || isNaN(number)) {
        alert('Please enter a number');
        numberInput.focus();
        return;
      }

      if (number < 0 || number > 100) {
        alert('Please enter a number between 0 and 100');
        numberInput.focus();
        return;
      }

      // Set submitting state
      isSubmitting = true;
      if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '⏳ Submitting...';
      }

      try {
        const formData = new FormData();
        formData.append('action', 'submit_number');
        formData.append('room_code', roomCode);
        formData.append('player_name', playerName);
        formData.append('chosen_number', number);

        console.log('Submitting number:', number, 'for player:', playerName);

        const res = await fetch('game_handler.php', {
          method: 'POST',
          body: formData
        });

        const data = await res.json();
        console.log('Submit response:', data);

        if (data.success) {
          // Clear the stored input value
          currentInputValue = '';

          // Update UI immediately
          if (numberInput) numberInput.disabled = true;
          if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '✅ Submitted!';
          }

          // Force immediate game state refresh
          setTimeout(() => {
            loadGameState();
          }, 500);

        } else {
          alert('Error: ' + data.message);
          // Reset button state on error
          if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '🎯 Submit Number';
          }
        }
      } catch (error) {
        console.error('Error submitting number:', error);
        alert('Failed to submit number. Please try again.');

        // Reset button state on error
        if (submitBtn) {
          submitBtn.disabled = false;
          submitBtn.innerHTML = '🎯 Submit Number';
        }
      } finally {
        isSubmitting = false;
      }
    }

    // Add event listener for input changes (using event delegation)
    document.addEventListener('input', function(e) {
      if (e.target && e.target.id === 'numberInput') {
        currentInputValue = e.target.value;
      }
    });

    // Add Enter key support for number input
    document.addEventListener('keydown', function(e) {
      if (e.target && e.target.id === 'numberInput' && e.key === 'Enter') {
        e.preventDefault();
        submitNumber();
      }
    });

    // Auto-refresh (reduced frequency to prevent input clearing)
    setInterval(() => {
      loadGameState();
      loadMessages();
    }, 3000); // Changed from 2000 to 3000ms

    // Initial load
    loadGameState();
    loadMessages();

    // Initialize AOS
    AOS.init();
  </script>

</body>
</html>
