<?php
include 'db.php';

$room_code = $_GET['code'] ?? '';
$player_name = $_GET['name'] ?? '';

if (empty($room_code) || empty($player_name)) {
    header('Location: index.php');
    exit;
}

// Check if database connection exists
if (!$conn) {
    die("Database connection failed. Please check your database configuration.");
}

// Check if game tables exist
$tables_check = $conn->query("SHOW TABLES LIKE 'game_rooms'");
if (!$tables_check || $tables_check->num_rows === 0) {
    echo "<!DOCTYPE html><html><head><title>Database Setup Required</title></head><body>";
    echo "<h1>Game Database Not Set Up</h1>";
    echo "<p>The game database tables are not set up yet.</p>";
    echo "<p><a href='setup_game_db.php'>Click here to set up the game database</a></p>";
    echo "<p><a href='index.php'>Back to Home</a></p>";
    echo "</body></html>";
    exit;
}

// Check if the game room exists
$room_check = $conn->prepare("SELECT room_code FROM game_rooms WHERE room_code = ?");
$room_check->bind_param("s", $room_code);
$room_check->execute();
$room_exists = $room_check->get_result()->num_rows > 0;

if (!$room_exists) {
    echo "<!DOCTYPE html><html><head><title>Room Not Found</title></head><body>";
    echo "<h1>Game Room Not Found</h1>";
    echo "<p>Room code '$room_code' does not exist.</p>";
    echo "<p><a href='join_game.php'>Join a different room</a></p>";
    echo "<p><a href='create_game.php'>Create a new room</a></p>";
    echo "</body></html>";
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Game Room <?php echo htmlspecialchars($room_code); ?> - QuickMeet</title>
  <meta name="description" content="QuickMeet Mathematical Strategy Game Room">
  <meta name="keywords" content="QuickMeet, math game, strategy game, multiplayer">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    body {
      font-family: var(--default-font);
      color: var(--default-color);
      background-color: var(--background-color);
      position: relative;
      margin: 0;
      padding: 0;
      height: 100vh;
      overflow: hidden;
    }

    body::before {
      background: linear-gradient(color-mix(in srgb, #000000, transparent 30%), color-mix(in srgb, #000000, transparent 30%)), url("assets/img/bg/abstract-bg-4.webp") no-repeat center center/cover;
      content: "";
      position: fixed;
      inset: 0;
      z-index: 0;
      pointer-events: none;
    }

    .game-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      position: relative;
      z-index: 1;
    }

    /* Game Area (Full Width) */
    .game-area {
      width: 100%;
      background: var(--surface-color);
      padding: 2rem;
      border-radius: 15px;
    }

    .game-header {
      text-align: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid rgba(206, 175, 127, 0.2);
    }

    .room-title {
      color: var(--heading-color);
      font-size: 2rem;
      font-weight: 700;
      margin: 0;
      font-family: var(--heading-font);
    }

    .game-status {
      color: var(--accent-color);
      font-size: 1.2rem;
      margin-top: 0.5rem;
    }

    .timer-display {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 1rem 2rem;
      border-radius: 15px;
      font-size: 2rem;
      font-weight: 700;
      text-align: center;
      margin: 1rem 0;
    }

    .players-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .player-card {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 12px;
      padding: 1rem;
      text-align: center;
    }

    .player-card.current-player {
      border-color: var(--accent-color);
      background: rgba(206, 175, 127, 0.1);
    }

    .player-card.eliminated {
      opacity: 0.5;
      background: rgba(220, 53, 69, 0.1);
      border-color: #dc3545;
    }

    .player-name {
      color: var(--heading-color);
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .player-score {
      color: var(--accent-color);
      font-size: 1.5rem;
      font-weight: 700;
    }

    .player-status {
      font-size: 0.9rem;
      margin-top: 0.5rem;
    }

    .number-input-section {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 15px;
      padding: 2rem;
      text-align: center;
      margin-bottom: 2rem;
    }

    .number-input {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(206, 175, 127, 0.2);
      border-radius: 12px;
      padding: 1rem 2rem;
      font-size: 2rem;
      color: var(--default-color);
      text-align: center;
      width: 200px;
      margin: 1rem;
    }

    .number-input:focus {
      outline: none;
      border-color: var(--accent-color);
      background: rgba(255, 255, 255, 0.15);
    }

    .submit-btn {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 1rem 3rem;
      border-radius: 12px;
      font-weight: 600;
      font-size: 1.2rem;
      border: none;
      cursor: pointer;
      margin: 1rem;
    }

    .submit-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .round-results {
      background: rgba(206, 175, 127, 0.1);
      border: 1px solid var(--accent-color);
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1rem;
    }

    /* Chat removed - Game only mode */

    /* Full Screen Number Selection Overlay */
    .number-selection-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .number-selection-container {
      width: 95%;
      max-width: 1200px;
      height: 95%;
      display: flex;
      flex-direction: column;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 20px;
      border: 2px solid var(--accent-color);
      overflow: hidden;
    }

    .selection-header {
      background: linear-gradient(135deg, var(--accent-color), #b8956f);
      color: var(--contrast-color);
      padding: 2rem;
      text-align: center;
      position: relative;
    }

    .selection-header h2 {
      margin: 0 0 1rem 0;
      font-size: 2.5rem;
      font-weight: 700;
    }

    .timer-display {
      font-size: 4rem;
      font-weight: 900;
      margin: 1rem 0;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .round-info {
      font-size: 1.5rem;
      opacity: 0.9;
    }

    .numbers-grid {
      flex: 1;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 8px;
      padding: 2rem;
      overflow-y: auto;
      background: rgba(0, 0, 0, 0.2);
    }

    .number-btn {
      aspect-ratio: 1;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      border: 2px solid rgba(206, 175, 127, 0.3);
      border-radius: 12px;
      color: var(--default-color);
      font-size: 1.2rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 60px;
    }

    .number-btn:hover {
      background: linear-gradient(135deg, var(--accent-color), #b8956f);
      color: var(--contrast-color);
      transform: scale(1.05);
      border-color: var(--accent-color);
      box-shadow: 0 5px 15px rgba(206, 175, 127, 0.4);
    }

    .number-btn.selected {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      border-color: #28a745;
      transform: scale(1.1);
      box-shadow: 0 8px 25px rgba(40, 167, 69, 0.5);
    }

    .selection-footer {
      background: rgba(0, 0, 0, 0.3);
      padding: 1.5rem;
      text-align: center;
    }

    .selected-number {
      font-size: 2rem;
      font-weight: 700;
      color: var(--accent-color);
      margin-bottom: 1rem;
    }

    .auto-submit-warning {
      font-size: 1.5rem;
      color: #ffc107;
      font-weight: 600;
      animation: pulse 1s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    /* Start Game Button */
    .start-game-btn {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 1rem 3rem;
      border-radius: 12px;
      font-weight: 600;
      font-size: 1.2rem;
      border: none;
      cursor: pointer;
      margin: 2rem auto;
      display: block;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .start-game-btn:hover {
      background: linear-gradient(135deg, #218838, #1e7e34);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    .start-game-btn:active {
      transform: translateY(0);
    }

    /* Waiting room styles */
    .waiting-room {
      text-align: center;
      padding: 2rem;
    }

    .players-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin: 2rem 0;
    }

    .player-card {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 12px;
      padding: 1.5rem;
      text-align: center;
    }

    .player-card.current-player {
      border-color: var(--accent-color);
      background: rgba(206, 175, 127, 0.1);
    }

    .player-name {
      color: var(--heading-color);
      font-weight: 600;
      font-size: 1.1rem;
      margin-bottom: 0.5rem;
    }

    .player-status {
      color: var(--default-color);
      font-size: 0.9rem;
    }

    /* Mobile responsive for number grid */
    @media (max-width: 768px) {
      .numbers-grid {
        grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
        gap: 6px;
        padding: 1rem;
      }

      .number-btn {
        font-size: 1rem;
        min-height: 50px;
      }

      .selection-header h2 {
        font-size: 2rem;
      }

      .timer-display {
        font-size: 3rem;
      }

      .players-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .start-game-btn {
        padding: 0.8rem 2rem;
        font-size: 1rem;
      }
    }

    /* Responsive */
    @media (max-width: 768px) {
      .game-container {
        padding: 1rem;
      }

      .game-area {
        padding: 1rem;
      }
    }

    /* Waiting room styles */
    .waiting-room {
      text-align: center;
      padding: 3rem;
    }

    .start-game-btn {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 1rem 3rem;
      border-radius: 12px;
      font-weight: 600;
      font-size: 1.2rem;
      border: none;
      cursor: pointer;
      margin: 2rem;
    }

    /* Break period styles */
    .break-period {
      text-align: center;
      padding: 2rem;
    }

    .break-period .timer-display {
      font-size: 2.5rem;
      margin-bottom: 2rem;
    }

    .break-period .round-results {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 15px;
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .break-period .players-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    /* Auto-submission indicator */
    .auto-submitted {
      background: rgba(255, 193, 7, 0.1);
      border-left-color: #ffc107;
    }

    .auto-submitted .sender::after {
      content: " ⏰";
      color: #ffc107;
    }
  </style>
</head>

<body>
  <!-- Full Screen Number Selection Overlay -->
  <div id="numberSelectionOverlay" class="number-selection-overlay" style="display: none;">
    <div class="number-selection-container">
      <div class="selection-header">
        <h2>Choose Your Number (0-100)</h2>
        <div class="timer-display" id="selectionTimer">60</div>
        <div class="round-info" id="roundInfo">Round 1</div>
      </div>

      <div class="numbers-grid" id="numbersGrid">
        <!-- Numbers 0-100 will be generated here -->
      </div>

      <div class="selection-footer">
        <div class="selected-number" id="selectedNumber" style="display: none;">
          Selected: <span id="selectedValue">-</span>
        </div>
        <div class="auto-submit-warning" id="autoSubmitWarning" style="display: none;">
          ⏰ Time's up! Auto-submitting...
        </div>
      </div>
    </div>
  </div>

  <div class="game-container">
    <!-- Game Area (Left Side) -->
    <div class="game-area">
      <div class="game-header">
        <h1 class="room-title">🎮 Room <?php echo htmlspecialchars($room_code); ?></h1>
        <div class="game-status" id="gameStatus">Loading...</div>
      </div>

      <!-- Game Content -->
      <div id="gameContent">
        <!-- Content will be loaded dynamically -->
      </div>
    </div>

    <!-- Chat Area Removed - Game Only Mode -->
  </div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <script>
    const roomCode = '<?php echo htmlspecialchars($room_code); ?>';
    const playerName = '<?php echo htmlspecialchars($player_name); ?>';
    let gameState = null;
    let playerId = localStorage.getItem(`player_id_${roomCode}`) || '';
    let currentInputValue = ''; // Store current input value
    let isSubmitting = false; // Prevent double submission

    // Chat functionality removed - Game only mode

    // Game functionality
    async function loadGameState() {
      try {
        console.log('Loading game state for room:', roomCode);
        const res = await fetch(`game_handler.php?action=get_game_state&room_code=${encodeURIComponent(roomCode)}`);
        const responseText = await res.text();
        console.log('Raw response:', responseText);

        let data;
        try {
          data = JSON.parse(responseText);
        } catch (e) {
          console.error('Failed to parse JSON:', e);
          console.error('Response was:', responseText);
          document.getElementById('gameContent').innerHTML = '<div class="error">Server error: Invalid response format</div>';
          return;
        }

        console.log('Game state data:', data);

        if (data.success) {
          gameState = data;
          updateGameDisplay();
        } else {
          console.error('Failed to load game state:', data.message);
          document.getElementById('gameContent').innerHTML = `<div class="error">Error: ${data.message}</div>`;
          document.getElementById('gameStatus').textContent = 'Error: ' + data.message;
        }
      } catch (error) {
        console.error('Error loading game state:', error);
        document.getElementById('gameContent').innerHTML = '<div class="error">Failed to connect to server</div>';
        document.getElementById('gameStatus').textContent = 'Connection error';
      }
    }

    function updateGameDisplay() {
      const status = document.getElementById('gameStatus');
      const content = document.getElementById('gameContent');

      if (!gameState) {
        console.log('No game state available');
        content.innerHTML = '<div class="error">Loading game state...</div>';
        return;
      }

      console.log('Updating game display with state:', gameState);

      const room = gameState.room;
      const players = gameState.players || [];

      console.log('Room status:', room.status, 'Players:', players.length);

      if (room.status === 'waiting') {
        const readyText = players.length >= 2 ? '✅ Ready to start!' : '⏳ Need more players';
        status.textContent = `${readyText} (${players.length} players joined)`;

        console.log('Rendering waiting room...');
        const waitingRoomHTML = renderWaitingRoom();
        console.log('Waiting room HTML:', waitingRoomHTML);
        content.innerHTML = waitingRoomHTML;

      } else if (room.status === 'playing') {
        status.textContent = `Round ${room.current_round} - ${gameState.time_remaining}s remaining (${players.filter(p => p.status === 'active').length} players)`;
        content.innerHTML = renderGamePlay();

      } else if (room.status === 'break') {
        status.textContent = `Round ${room.current_round} Results - Next round in ${gameState.break_remaining}s`;
        content.innerHTML = renderBreakPeriod();

      } else if (room.status === 'finished') {
        status.textContent = 'Game Finished!';
        content.innerHTML = renderGameFinished();

      } else {
        console.log('Unknown room status:', room.status);
        content.innerHTML = '<div class="error">Unknown game status: ' + room.status + '</div>';
      }
    }

    function renderWaitingRoom() {
      const players = gameState.players;
      console.log('Rendering waiting room with players:', players);

      let html = '<div class="waiting-room">';
      html += '<h3 style="color: var(--heading-color);">Game Room Ready</h3>';

      // Room sharing section
      html += '<div style="background: rgba(206, 175, 127, 0.1); border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; text-align: center;">';
      html += '<h4 style="color: var(--accent-color); margin-bottom: 1rem;">📤 Invite More Players</h4>';
      html += `<div style="background: rgba(255, 255, 255, 0.1); border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">`;
      html += `<strong style="color: var(--heading-color);">Room Code: ${roomCode}</strong>`;
      html += `<button onclick="copyRoomCode()" style="margin-left: 1rem; background: var(--accent-color); color: var(--contrast-color); border: none; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer;">📋 Copy</button>`;
      html += '</div>';
      html += '<p style="color: var(--default-color); margin: 0; font-size: 0.9rem;">Share this code with friends • They can join anytime before the game starts!</p>';
      html += '</div>';

      html += '<div class="players-grid">';

      players.forEach(player => {
        html += `<div class="player-card">
          <div class="player-name">${player.player_name}</div>
          <div class="player-status">${player.player_name === gameState.room.host_name ? '👑 Host' : '👤 Player'}</div>
        </div>`;
      });

      // Add empty slots (only show if less than 5 players)
      if (players.length < 5) {
        for (let i = players.length; i < 5; i++) {
          html += `<div class="player-card" style="opacity: 0.3;">
            <div class="player-name">Open Slot</div>
            <div class="player-status">Anyone can join</div>
          </div>`;
        }
      }

      html += '</div>';

      // Any player can start the game if there are at least 2 players
      console.log('Player count for start button:', players.length);
      if (players.length >= 2) {
        console.log('Adding start button');
        html += `<div style="text-align: center; margin: 2rem 0;">
          <button class="start-game-btn" onclick="startGame()" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 1rem 3rem; border-radius: 12px; font-weight: 600; font-size: 1.2rem; border: none; cursor: pointer; display: inline-block;">
            🎮 Start Game Now (${players.length} players)
          </button>
        </div>`;
        html += '<div style="text-align: center; margin-top: 1rem;">';
        html += '<p style="color: var(--default-color); margin: 0.5rem 0; font-size: 0.9rem;">🎮 Ready to play! Any player can start • 2-5 players supported</p>';
        if (players.length < 5) {
          html += '<p style="color: var(--accent-color); margin: 0.5rem 0; font-size: 0.85rem;">💡 More players can join before starting (up to 5 total)</p>';
        }
        html += '</div>';
      } else {
        console.log('Not enough players for start button');
        html += '<div style="text-align: center; margin: 2rem 0; padding: 2rem; background: rgba(255, 193, 7, 0.1); border-radius: 12px;">';
        html += '<p style="color: #ffc107; margin: 0; font-size: 1.1rem; font-weight: 600;">⏳ Waiting for one more player...</p>';
        html += '<p style="color: var(--default-color); margin: 0.5rem 0 0 0; font-size: 0.9rem;">Need 2-5 players to start the game</p>';
        html += '<p style="color: var(--default-color); margin: 0.5rem 0 0 0; font-size: 0.9rem;">Share the room code with friends to join!</p>';
        html += '</div>';
      }

      html += '</div>';
      return html;
    }

    function renderGamePlay() {
      const currentPlayer = gameState.players.find(p => p.player_name === playerName);
      const hasSubmitted = gameState.submissions[playerName] || false;

      // Show full-screen number selection if round is active and player hasn't submitted
      if (gameState.time_remaining > 0 && !hasSubmitted && currentPlayer && currentPlayer.status === 'active') {
        showNumberSelection();
      } else {
        hideNumberSelection();
      }

      let html = '';

      // Timer
      html += `<div class="timer-display">⏰ ${gameState.time_remaining}s</div>`;

      // Players grid
      html += '<div class="players-grid">';
      gameState.players.forEach(player => {
        const isCurrentPlayer = player.player_name === playerName;
        const playerSubmitted = gameState.submissions[player.player_name] || false;

        html += `<div class="player-card ${isCurrentPlayer ? 'current-player' : ''} ${player.status === 'eliminated' ? 'eliminated' : ''}">
          <div class="player-name">${player.player_name}</div>
          <div class="player-score">${player.score} pts</div>
          <div class="player-status">
            ${player.status === 'eliminated' ? '💀 Eliminated' :
              playerSubmitted ? '✅ Submitted' : '⏳ Thinking...'}
          </div>
        </div>`;
      });
      html += '</div>';

      // Submission status display
      const submissions = gameState.submissions || {};
      const submittedCount = Object.values(submissions).filter(Boolean).length;
      const totalPlayers = players.filter(p => p.status === 'active').length;
      const allSubmitted = submittedCount === totalPlayers && totalPlayers > 0;

      if (allSubmitted) {
        html += `<div style="background: rgba(40, 167, 69, 0.2); padding: 1.5rem; border-radius: 12px; margin: 1.5rem 0; text-align: center; border: 2px solid #28a745;">
          <h4 style="color: #28a745; margin-bottom: 1rem;">✅ All Players Submitted!</h4>
          <p style="color: var(--default-color); margin: 0; font-size: 1.1rem;">
            ⏰ Round continues for full duration<br>
            <strong>${gameState.time_remaining} seconds remaining</strong>
          </p>
          <p style="color: var(--accent-color); margin: 0.5rem 0 0 0; font-size: 0.9rem;">
            💡 Results will be shown when timer reaches zero
          </p>
        </div>`;
      }

      // Number input section removed - using full-screen overlay instead
      
      // Last round results
      if (gameState.last_result) {
        const result = gameState.last_result;
        html += `<div class="round-results">
          <h4 style="color: var(--accent-color);">📊 Previous Round Results</h4>
          <p><strong>Average:</strong> ${parseFloat(result.average_number).toFixed(2)}</p>
          <p><strong>Target (×0.8):</strong> ${parseFloat(result.target_number).toFixed(2)}</p>
          <p><strong>Winner:</strong> 🏆 ${result.winner_name}</p>
        </div>`;
      }
      
      return html;
    }

    function renderBreakPeriod() {
      let html = '<div class="break-period">';

      // Break timer
      html += `<div class="timer-display" style="background: linear-gradient(135deg, #28a745, #20c997);">
        ⏸️ Break Time: ${gameState.break_remaining}s
      </div>`;

      // Round results
      if (gameState.last_result) {
        const result = gameState.last_result;
        const activePlayers = players.filter(p => p.status === 'active').length;

        html += `<div class="round-results" style="margin-bottom: 2rem;">
          <h3 style="color: var(--accent-color); text-align: center; margin-bottom: 1.5rem;">
            🎯 Round ${result.round_number} Results
          </h3>

          <!-- Progressive Rules Status -->
          <div style="background: rgba(206, 175, 127, 0.1); padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem;">
            <h5 style="color: var(--accent-color); margin-bottom: 0.5rem;">⚡ Active Rules (${activePlayers} players):</h5>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 0.5rem;">
              <div style="color: ${activePlayers <= 4 ? '#28a745' : '#6c757d'};">
                ${activePlayers <= 4 ? '✅' : '❌'} Rule 1: Same number = Disqualified
              </div>
              <div style="color: ${activePlayers <= 3 ? '#28a745' : '#6c757d'};">
                ${activePlayers <= 3 ? '✅' : '❌'} Rule 2: Loser gets -2 points
              </div>
              <div style="color: ${activePlayers <= 2 ? '#28a745' : '#6c757d'};">
                ${activePlayers <= 2 ? '✅' : '❌'} Rule 3: 0 vs 100 special rule
              </div>
            </div>
          </div>

          <!-- Player Choices Display -->
          <div style="background: rgba(255, 255, 255, 0.05); padding: 1.5rem; border-radius: 12px; margin-bottom: 1.5rem;">
            <h4 style="color: var(--heading-color); text-align: center; margin-bottom: 1rem;">Player Choices</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">`;

        // Show all player choices
        if (gameState.last_round_choices && gameState.last_round_choices.length > 0) {
          gameState.last_round_choices.forEach(choice => {
            const isWinner = choice.player_name === result.winner_name;
            html += `<div style="background: ${isWinner ? 'rgba(40, 167, 69, 0.2)' : 'rgba(255, 255, 255, 0.05)'}; padding: 1rem; border-radius: 8px; text-align: center; border: ${isWinner ? '2px solid #28a745' : '1px solid rgba(206, 175, 127, 0.2)'};">
              <div style="color: var(--heading-color); font-weight: 600; margin-bottom: 0.5rem;">${isWinner ? '🏆' : '🎯'} ${choice.player_name}</div>
              <div style="color: var(--accent-color); font-size: 1.5rem; font-weight: 700;">${choice.chosen_number}</div>
            </div>`;
          });
        }

        html += `</div></div>

          <!-- Calculation Results -->
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
            <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 12px; text-align: center;">
              <div style="color: var(--accent-color); font-size: 0.9rem; margin-bottom: 0.5rem;">Average</div>
              <div style="color: var(--heading-color); font-size: 1.5rem; font-weight: 700;">${parseFloat(result.average_number).toFixed(2)}</div>
            </div>
            <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 12px; text-align: center;">
              <div style="color: var(--accent-color); font-size: 0.9rem; margin-bottom: 0.5rem;">Target (×0.8)</div>
              <div style="color: var(--heading-color); font-size: 1.5rem; font-weight: 700;">${parseFloat(result.target_number).toFixed(2)}</div>
            </div>
            <div style="background: rgba(40, 167, 69, 0.2); padding: 1rem; border-radius: 12px; text-align: center;">
              <div style="color: #28a745; font-size: 0.9rem; margin-bottom: 0.5rem;">Winner</div>
              <div style="color: var(--heading-color); font-size: 1.5rem; font-weight: 700;">🏆 ${result.winner_name}</div>
            </div>
          </div>

          <!-- Rule Messages -->
          ${result.rule_messages ? `
          <div style="background: rgba(255, 193, 7, 0.1); padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
            <h5 style="color: #ffc107; margin-bottom: 0.5rem;">⚡ Rule Effects:</h5>
            <div style="color: var(--default-color); font-size: 0.9rem;">
              ${result.rule_messages.split(' | ').map(msg => `<div style="margin-bottom: 0.25rem;">• ${msg}</div>`).join('')}
            </div>
          </div>
          ` : ''}

          <!-- Calculation Explanation -->
          <div style="background: rgba(206, 175, 127, 0.1); padding: 1rem; border-radius: 8px; text-align: center;">
            <p style="color: var(--default-color); margin: 0; font-size: 0.9rem;">
              📊 Calculation: Average × 0.8 = Target | Progressive rules apply based on player count
            </p>
          </div>
        </div>`;
      }

      // Current scores
      html += '<div class="players-grid">';
      html += '<h4 style="color: var(--heading-color); text-align: center; margin-bottom: 1rem; grid-column: 1 / -1;">Current Scores</h4>';

      // Sort players by score
      const sortedPlayers = [...gameState.players].sort((a, b) => b.score - a.score);

      sortedPlayers.forEach((player, index) => {
        const isCurrentPlayer = player.player_name === playerName;
        const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅';

        html += `<div class="player-card ${isCurrentPlayer ? 'current-player' : ''} ${player.status === 'eliminated' ? 'eliminated' : ''}">
          <div class="player-name">${rankIcon} ${player.player_name}</div>
          <div class="player-score">${player.score} pts</div>
          <div class="player-status">
            ${player.status === 'eliminated' ? '💀 Eliminated' :
              index === 0 ? '👑 Leading' : '🎯 Active'}
          </div>
        </div>`;
      });

      html += '</div>';

      // Next round info
      html += `<div style="text-align: center; margin-top: 2rem; padding: 1.5rem; background: rgba(206, 175, 127, 0.1); border-radius: 12px;">
        <h4 style="color: var(--accent-color); margin-bottom: 1rem;">🚀 Get Ready!</h4>
        <p style="color: var(--default-color); margin: 0;">Round ${gameState.room.current_round + 1} starts automatically in ${gameState.break_remaining} seconds</p>
      </div>`;

      html += '</div>';
      return html;
    }

    function renderGameFinished() {
      const winner = gameState.players.find(p => p.status === 'active');
      
      let html = '<div class="waiting-room">';
      html += '<h2 style="color: var(--accent-color);">🎉 Game Over!</h2>';
      
      if (winner) {
        html += `<h3 style="color: var(--heading-color);">🏆 Winner: ${winner.player_name}</h3>`;
      }
      
      html += '<h4 style="color: var(--heading-color);">Final Scores:</h4>';
      html += '<div class="players-grid">';
      
      // Sort players by score
      const sortedPlayers = [...gameState.players].sort((a, b) => b.score - a.score);
      
      sortedPlayers.forEach((player, index) => {
        html += `<div class="player-card ${player.status === 'eliminated' ? 'eliminated' : ''}">
          <div class="player-name">${index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅'} ${player.player_name}</div>
          <div class="player-score">${player.score} pts</div>
          <div class="player-status">${player.status === 'eliminated' ? 'Eliminated' : 'Survived'}</div>
        </div>`;
      });
      
      html += '</div>';
      html += '<a href="index.php" class="start-game-btn">🏠 Back to Home</a>';
      html += '</div>';

      // DEBUG: Always add a test start button for debugging
      html += '<div style="text-align: center; margin: 2rem 0; padding: 1rem; background: rgba(255, 0, 0, 0.1); border: 2px solid red; border-radius: 8px;">';
      html += '<h4 style="color: red;">DEBUG: Test Start Button</h4>';
      html += '<button onclick="startGame()" style="background: red; color: white; padding: 1rem 2rem; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem;">🧪 DEBUG START GAME</button>';
      html += '<p style="color: red; font-size: 0.8rem; margin: 0.5rem 0 0 0;">This is a debug button - remove after testing</p>';
      html += '</div>';

      console.log('Final waiting room HTML:', html);
      return html;
    }

    async function startGame() {
      try {
        const formData = new FormData();
        formData.append('action', 'start_game');
        formData.append('room_code', roomCode);
        formData.append('player_name', playerName);

        const res = await fetch('game_handler.php', {
          method: 'POST',
          body: formData
        });

        const data = await res.json();
        if (!data.success) {
          alert('Error: ' + data.message);
        }
      } catch (error) {
        console.error('Error starting game:', error);
        alert('Failed to start game');
      }
    }

    // Old submitNumber function removed - using full-screen selection instead

    // Old input event listeners removed - using full-screen selection instead

    // Auto-refresh with different frequencies based on game state
    function autoRefresh() {
      loadGameState();

      // Check break period if in break status
      if (gameState && gameState.room.status === 'break') {
        fetch(`game_handler.php?action=check_break&room_code=${encodeURIComponent(roomCode)}`)
          .then(response => response.json())
          .then(data => {
            if (data.success && data.new_round) {
              // Force immediate refresh when new round starts
              setTimeout(() => loadGameState(), 500);
            }
          })
          .catch(error => console.error('Break check error:', error));
      }
    }

    // Auto-refresh every 2 seconds for responsive gameplay
    setInterval(autoRefresh, 2000);

    // Initial load
    loadGameState();

    // Full-screen number selection functions
    let selectedNumber = null;
    let selectionTimer = null;

    function showNumberSelection() {
      const overlay = document.getElementById('numberSelectionOverlay');
      const numbersGrid = document.getElementById('numbersGrid');
      const timerDisplay = document.getElementById('selectionTimer');
      const roundInfo = document.getElementById('roundInfo');

      // Update round info
      if (gameState && gameState.room) {
        roundInfo.textContent = `Round ${gameState.room.current_round}`;
      }

      // Generate numbers 0-100
      numbersGrid.innerHTML = '';
      for (let i = 0; i <= 100; i++) {
        const btn = document.createElement('button');
        btn.className = 'number-btn';
        btn.textContent = i;
        btn.onclick = () => selectNumber(i);
        numbersGrid.appendChild(btn);
      }

      // Show overlay
      overlay.style.display = 'flex';

      // Start timer
      startSelectionTimer();
    }

    function hideNumberSelection() {
      const overlay = document.getElementById('numberSelectionOverlay');
      overlay.style.display = 'none';

      if (selectionTimer) {
        clearInterval(selectionTimer);
        selectionTimer = null;
      }
    }

    function selectNumber(number) {
      selectedNumber = number;

      // Update visual selection
      document.querySelectorAll('.number-btn').forEach(btn => {
        btn.classList.remove('selected');
      });
      event.target.classList.add('selected');

      // Show selected number
      const selectedDisplay = document.getElementById('selectedNumber');
      const selectedValue = document.getElementById('selectedValue');
      selectedValue.textContent = number;
      selectedDisplay.style.display = 'block';

      // Auto-submit after selection
      setTimeout(() => {
        if (selectedNumber === number) {
          submitSelectedNumber();
        }
      }, 500);
    }

    function startSelectionTimer() {
      if (selectionTimer) {
        clearInterval(selectionTimer);
      }

      let timeLeft = gameState ? gameState.time_remaining : 60;
      const timerDisplay = document.getElementById('selectionTimer');

      selectionTimer = setInterval(() => {
        timeLeft--;
        timerDisplay.textContent = timeLeft;

        // Change color as time runs out
        if (timeLeft <= 10) {
          timerDisplay.style.color = '#dc3545';
        } else if (timeLeft <= 30) {
          timerDisplay.style.color = '#ffc107';
        }

        if (timeLeft <= 0) {
          clearInterval(selectionTimer);
          autoSubmitNumber();
        }
      }, 1000);
    }

    function autoSubmitNumber() {
      if (selectedNumber === null) {
        selectedNumber = Math.floor(Math.random() * 101);
      }

      const warningDiv = document.getElementById('autoSubmitWarning');
      warningDiv.style.display = 'block';

      setTimeout(() => {
        submitSelectedNumber();
      }, 1000);
    }

    async function submitSelectedNumber() {
      if (selectedNumber === null) return;

      try {
        const formData = new FormData();
        formData.append('action', 'submit_number');
        formData.append('room_code', roomCode);
        formData.append('player_name', playerName);
        formData.append('chosen_number', selectedNumber);

        const res = await fetch('game_handler.php', {
          method: 'POST',
          body: formData
        });

        const data = await res.json();

        if (data.success) {
          hideNumberSelection();
          selectedNumber = null;
          // Force immediate game state refresh
          setTimeout(() => loadGameState(), 500);
        } else {
          alert('Error: ' + data.message);
        }
      } catch (error) {
        console.error('Error submitting number:', error);
        alert('Failed to submit number');
      }
    }

    // Copy room code function
    function copyRoomCode() {
      navigator.clipboard.writeText(roomCode).then(() => {
        // Show temporary feedback
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '✅ Copied!';
        button.style.background = '#28a745';

        setTimeout(() => {
          button.innerHTML = originalText;
          button.style.background = 'var(--accent-color)';
        }, 2000);
      }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = roomCode;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        alert('Room code copied: ' + roomCode);
      });
    }

    // Initialize AOS
    AOS.init();
  </script>

</body>
</html>
