<?php
// Setup script to create upload directories and update database
// Run this file once to set up file sharing functionality

include 'db.php';

echo "<h2>Setting up File Sharing Functionality</h2>";

// Create upload directories
$directories = [
    'uploads',
    'uploads/images',
    'uploads/documents',
    'uploads/other'
];

echo "<h3>Creating Upload Directories:</h3>";
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ Created directory: $dir<br>";
        } else {
            echo "❌ Failed to create directory: $dir<br>";
        }
    } else {
        echo "✅ Directory already exists: $dir<br>";
    }
}

// Update database schema
echo "<h3>Updating Database Schema:</h3>";

try {
    // Add file columns to messages table
    $queries = [
        "ALTER TABLE messages ADD COLUMN file_path VARCHAR(255) DEFAULT NULL",
        "ALTER TABLE messages ADD COLUMN file_name VARCHAR(255) DEFAULT NULL", 
        "ALTER TABLE messages ADD COLUMN file_type VARCHAR(50) DEFAULT NULL",
        "ALTER TABLE messages ADD COLUMN file_size INT DEFAULT NULL",
        "ALTER TABLE messages MODIFY COLUMN message TEXT NULL",
        "ALTER TABLE messages ADD INDEX idx_room_timestamp (room_code, timestamp)",
        "ALTER TABLE messages ADD INDEX idx_file_type (file_type)"
    ];
    
    foreach ($queries as $query) {
        try {
            $conn->query($query);
            echo "✅ Executed: " . substr($query, 0, 50) . "...<br>";
        } catch (Exception $e) {
            // Column might already exist, that's okay
            if (strpos($e->getMessage(), 'Duplicate column name') !== false || 
                strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "ℹ️ Already exists: " . substr($query, 0, 50) . "...<br>";
            } else {
                echo "❌ Error: " . $e->getMessage() . "<br>";
            }
        }
    }
    
    echo "<h3>✅ Database setup completed!</h3>";
    
} catch (Exception $e) {
    echo "<h3>❌ Database setup failed: " . $e->getMessage() . "</h3>";
}

// Create .htaccess for uploads directory security
$htaccess_content = "# Prevent direct access to uploaded files
Options -Indexes

# Allow only specific file types
<FilesMatch \"\\.(jpg|jpeg|png|gif|webp|bmp|pdf|doc|docx|txt|rtf|xls|xlsx|ppt|pptx|zip|rar|7z|mp3|mp4|avi|mov)$\">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Deny access to PHP files in uploads
<FilesMatch \"\\.php$\">
    Order Deny,Allow
    Deny from all
</FilesMatch>";

if (file_put_contents('uploads/.htaccess', $htaccess_content)) {
    echo "✅ Created security .htaccess file<br>";
} else {
    echo "❌ Failed to create .htaccess file<br>";
}

echo "<h3>🎉 File Sharing Setup Complete!</h3>";
echo "<p>You can now:</p>";
echo "<ul>";
echo "<li>📷 Share images (JPG, PNG, GIF, WebP, BMP)</li>";
echo "<li>📄 Share documents (PDF, DOC, DOCX, TXT, RTF)</li>";
echo "<li>📊 Share spreadsheets (XLS, XLSX)</li>";
echo "<li>📋 Share presentations (PPT, PPTX)</li>";
echo "<li>📦 Share archives (ZIP, RAR, 7Z)</li>";
echo "<li>🎵 Share audio/video files (MP3, MP4, AVI, MOV)</li>";
echo "</ul>";
echo "<p><strong>File size limit:</strong> 10MB per file</p>";
echo "<p><a href='index.php'>← Back to QuickMeet</a></p>";
?>
