-- Fix messages table for game integration
-- Run this SQL to add missing columns to messages table

-- Add game_room column if it doesn't exist
ALTER TABLE messages ADD COLUMN IF NOT EXISTS game_room BOOLEAN DEFAULT FALSE;

-- Add message_type column if it doesn't exist  
ALTER TABLE messages ADD COLUMN IF NOT EXISTS message_type ENUM('chat', 'system', 'game') DEFAULT 'chat';

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_messages_game_room ON messages(room_code, game_room);

-- Verify the changes
DESCRIBE messages;
