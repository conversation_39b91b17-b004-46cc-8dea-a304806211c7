<!DOCTYPE html>
<html>
<head>
    <title>Quick Game Setup - QuickMeet</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🎮 QuickMeet Game Quick Setup</h1>
    
    <?php
    include 'db.php';
    
    if (!$conn) {
        echo "<div class='error'>❌ Database connection failed. Please check db.php configuration.</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully</div>";
    
    // Run the complete database setup
    $sql_file = file_get_contents('complete_game_database.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql_file)));
    
    $success_count = 0;
    $error_count = 0;
    
    echo "<h2>🛠️ Setting up database...</h2>";
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty lines and comments
        }
        
        if ($conn->query($statement)) {
            $success_count++;
            if (strpos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?(\w+)/', $statement, $matches);
                $table_name = $matches[1] ?? 'unknown';
                echo "<div class='success'>✅ Created table: $table_name</div>";
            }
        } else {
            $error_count++;
            echo "<div class='error'>❌ Error: " . $conn->error . "</div>";
            echo "<pre>" . htmlspecialchars($statement) . "</pre>";
        }
    }
    
    echo "<h2>📊 Setup Summary</h2>";
    echo "<p>✅ Successful operations: $success_count</p>";
    echo "<p>❌ Errors: $error_count</p>";
    
    if ($error_count === 0) {
        echo "<div class='success'>";
        echo "<h3>🎉 Database setup completed successfully!</h3>";
        echo "<p>Your QuickMeet game database is ready to use.</p>";
        echo "</div>";
        
        // Create a test game room
        $test_room_code = 'TEST01';
        $test_player = 'TestPlayer';
        
        echo "<h2>🧪 Creating test game room...</h2>";
        
        // Check if test room already exists
        $check_room = $conn->prepare("SELECT room_code FROM game_rooms WHERE room_code = ?");
        $check_room->bind_param("s", $test_room_code);
        $check_room->execute();
        
        if ($check_room->get_result()->num_rows === 0) {
            // Create test room
            $create_room = $conn->prepare("INSERT INTO game_rooms (room_code, room_name, host_name, status) VALUES (?, 'Test Game Room', ?, 'waiting')");
            $create_room->bind_param("ss", $test_room_code, $test_player);
            
            if ($create_room->execute()) {
                echo "<div class='success'>✅ Test room created: $test_room_code</div>";
                
                // Add test player
                $add_player = $conn->prepare("INSERT INTO game_players (room_code, player_name, player_id, score, status) VALUES (?, ?, ?, 0, 'active')");
                $player_id = uniqid();
                $add_player->bind_param("sss", $test_room_code, $test_player, $player_id);
                
                if ($add_player->execute()) {
                    echo "<div class='success'>✅ Test player added: $test_player</div>";
                } else {
                    echo "<div class='error'>❌ Failed to add test player: " . $conn->error . "</div>";
                }
            } else {
                echo "<div class='error'>❌ Failed to create test room: " . $conn->error . "</div>";
            }
        } else {
            echo "<div class='info'>ℹ️ Test room already exists: $test_room_code</div>";
        }
        
        echo "<h2>🎮 Ready to Play!</h2>";
        echo "<div class='info'>";
        echo "<p>Your game is now ready! You can:</p>";
        echo "<ul>";
        echo "<li><a href='index.php' class='btn'>🏠 Go to Homepage</a></li>";
        echo "<li><a href='create_game.php' class='btn'>🎮 Create New Game</a></li>";
        echo "<li><a href='game_room.php?code=$test_room_code&name=$test_player' class='btn'>🧪 Test Game Room</a></li>";
        echo "<li><a href='debug_game_room.php?code=$test_room_code&name=$test_player' class='btn'>🔍 Debug Game Room</a></li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>";
        echo "<h3>⚠️ Setup completed with errors</h3>";
        echo "<p>Some operations failed. Please check the errors above.</p>";
        echo "</div>";
    }
    
    // Show current database status
    echo "<h2>📋 Database Status</h2>";
    $tables = ['game_rooms', 'game_players', 'game_rounds', 'game_results'];
    
    foreach ($tables as $table) {
        $check = $conn->query("SHOW TABLES LIKE '$table'");
        if ($check && $check->num_rows > 0) {
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_result->fetch_assoc()['count'];
            echo "<div class='success'>✅ Table '$table' exists ($count records)</div>";
        } else {
            echo "<div class='error'>❌ Table '$table' missing</div>";
        }
    }
    
    $conn->close();
    ?>
    
    <hr>
    <p style="text-align: center; color: #666;">
        <small>QuickMeet Mathematical Strategy Game Setup Tool</small>
    </p>
</body>
</html>
