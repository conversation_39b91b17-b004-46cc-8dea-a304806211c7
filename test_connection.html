<!DOCTYPE html>
<html>
<head>
    <title>Test Connection - QuickMeet</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🧪 Connection Test Tool</h1>
    
    <div class="test-section">
        <h3>1. Test Basic Game Handler</h3>
        <button onclick="testBasic()">Test Basic Connection</button>
        <div id="basicResult"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test Number Submission</h3>
        <input type="text" id="roomCode" placeholder="Room Code (e.g., 3J1H26)" value="3J1H26">
        <input type="text" id="playerName" placeholder="Player Name" value="TestUser">
        <input type="number" id="chosenNumber" placeholder="Number (0-100)" value="42" min="0" max="100">
        <br>
        <button onclick="testSubmission()">Test Number Submission</button>
        <button onclick="testDebugSubmission()">Test Debug Submission</button>
        <div id="submissionResult"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Test Game State</h3>
        <input type="text" id="stateRoomCode" placeholder="Room Code" value="3J1H26">
        <button onclick="testGameState()">Get Game State</button>
        <div id="stateResult"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Start Game Test</h3>
        <input type="text" id="startRoomCode" placeholder="Room Code" value="3J1H26">
        <input type="text" id="startPlayerName" placeholder="Player Name" value="TestUser">
        <button onclick="testStartGame()">Start Game</button>
        <div id="startResult"></div>
    </div>

    <script>
        async function testBasic() {
            const result = document.getElementById('basicResult');
            result.innerHTML = '<p>Testing...</p>';
            
            try {
                const response = await fetch('game_handler.php?action=test');
                const text = await response.text();
                
                result.innerHTML = `
                    <div class="success">
                        <strong>Status:</strong> ${response.status}<br>
                        <strong>Response:</strong><br>
                        <pre>${text}</pre>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testSubmission() {
            const result = document.getElementById('submissionResult');
            const roomCode = document.getElementById('roomCode').value;
            const playerName = document.getElementById('playerName').value;
            const chosenNumber = document.getElementById('chosenNumber').value;
            
            result.innerHTML = '<p>Testing number submission...</p>';
            
            try {
                const formData = new FormData();
                formData.append('action', 'submit_number');
                formData.append('room_code', roomCode);
                formData.append('player_name', playerName);
                formData.append('chosen_number', chosenNumber);
                
                const response = await fetch('game_handler.php', {
                    method: 'POST',
                    body: formData
                });
                
                const text = await response.text();
                
                result.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <strong>Status:</strong> ${response.status}<br>
                        <strong>Response:</strong><br>
                        <pre>${text}</pre>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testDebugSubmission() {
            const result = document.getElementById('submissionResult');
            const roomCode = document.getElementById('roomCode').value;
            const playerName = document.getElementById('playerName').value;
            const chosenNumber = document.getElementById('chosenNumber').value;
            
            result.innerHTML = '<p>Testing debug submission...</p>';
            
            try {
                const formData = new FormData();
                formData.append('room_code', roomCode);
                formData.append('player_name', playerName);
                formData.append('chosen_number', chosenNumber);
                
                const response = await fetch('debug_submit.php', {
                    method: 'POST',
                    body: formData
                });
                
                const text = await response.text();
                
                result.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <strong>Debug Status:</strong> ${response.status}<br>
                        <strong>Debug Response:</strong><br>
                        <pre>${text}</pre>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">Debug Error: ${error.message}</div>`;
            }
        }
        
        async function testGameState() {
            const result = document.getElementById('stateResult');
            const roomCode = document.getElementById('stateRoomCode').value;
            
            result.innerHTML = '<p>Getting game state...</p>';
            
            try {
                const response = await fetch(`game_handler.php?action=get_game_state&room_code=${encodeURIComponent(roomCode)}`);
                const text = await response.text();
                
                result.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <strong>Status:</strong> ${response.status}<br>
                        <strong>Response:</strong><br>
                        <pre>${text}</pre>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testStartGame() {
            const result = document.getElementById('startResult');
            const roomCode = document.getElementById('startRoomCode').value;
            const playerName = document.getElementById('startPlayerName').value;
            
            result.innerHTML = '<p>Starting game...</p>';
            
            try {
                const formData = new FormData();
                formData.append('action', 'start_game');
                formData.append('room_code', roomCode);
                formData.append('player_name', playerName);
                
                const response = await fetch('game_handler.php', {
                    method: 'POST',
                    body: formData
                });
                
                const text = await response.text();
                
                result.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <strong>Status:</strong> ${response.status}<br>
                        <strong>Response:</strong><br>
                        <pre>${text}</pre>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
