<?php include 'db.php'; ?>
<?php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $room_code = rand(100000, 999999);
    $name = $_POST['name'];
    $phone = $_POST['phone'];
    $age = $_POST['age'];
    $gender = $_POST['gender'];
    $password = $_POST['password'];

    $stmt = $conn->prepare("INSERT INTO rooms (room_code, name, phone, age, gender, password) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssss", $room_code, $name, $phone, $age, $gender, $password);
    $stmt->execute();

    header("Location: room.php?code=$room_code&name=" . urlencode($name));
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Create Room - QuickMeet</title>
  <meta name="description" content="Create a new chat room and start connecting with friends instantly.">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    .form-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      padding: 120px 0 80px 0;
    }

    .form-card {
      background: var(--surface-color);
      border-radius: 20px;
      padding: 3rem;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(206, 175, 127, 0.1);
      max-width: 600px;
      margin: 0 auto;
    }

    .form-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .form-header h2 {
      color: var(--heading-color);
      margin-bottom: 0.5rem;
    }

    .form-header p {
      color: var(--default-color);
      opacity: 0.8;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      color: var(--heading-color);
      font-weight: 500;
    }

    .form-control, .form-select {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 10px;
      padding: 12px 16px;
      color: var(--default-color);
      font-size: 1rem;
      transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
      background: rgba(255, 255, 255, 0.1);
      border-color: var(--accent-color);
      box-shadow: 0 0 0 0.2rem rgba(206, 175, 127, 0.25);
      color: var(--default-color);
    }

    .form-select option {
      background: var(--surface-color);
      color: var(--default-color);
      padding: 10px;
    }

    .form-select option:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .form-select option:checked {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .form-control::placeholder {
      color: rgba(241, 243, 245, 0.6);
    }

    .btn-create {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 15px 40px;
      border-radius: 50px;
      border: none;
      font-weight: 600;
      font-size: 1.1rem;
      width: 100%;
      transition: all 0.3s ease;
      margin-top: 1rem;
    }

    .btn-create:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 30px rgba(206, 175, 127, 0.3);
      color: var(--contrast-color);
    }

    .back-link {
      color: var(--accent-color);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      margin-bottom: 2rem;
      transition: color 0.3s ease;
    }

    .back-link:hover {
      color: #d4b896;
    }

    .back-link i {
      margin-right: 0.5rem;
    }

    .info-box {
      background: rgba(206, 175, 127, 0.1);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 10px;
      padding: 1rem;
      margin-top: 1.5rem;
    }

    .info-box h6 {
      color: var(--accent-color);
      margin-bottom: 0.5rem;
    }

    .info-box p {
      margin: 0;
      font-size: 0.9rem;
      opacity: 0.8;
    }
  </style>
</head>

<body>

  <!-- Header -->
  <header id="header" class="header d-flex align-items-center sticky-top">
    <div class="container position-relative d-flex align-items-center justify-content-between">

      <a href="index.php" class="logo d-flex align-items-center me-auto me-xl-0">
        <img src="assets/img/chatbot-logo.png" alt="QuickMeet Logo" style="height: 40px; margin-right: 0.5rem;">
        <h1 class="sitename">QuickMeet</h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.php">Home</a></li>
          <li><a href="index.php#features">Features</a></li>
          <li><a href="index.php#how-it-works">How It Works</a></li>
          <li><a href="https://quickf.free.nf/" target="_blank">Quick</a></li>
          <li><a href="admin.php">Admin</a></li>
          <li><a href="create_room.php" class="active">Create Room</a></li>
          <li><a href="join_room.php">Join Room</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

    </div>
  </header>

  <main class="main">
    <section class="form-container">
      <div class="container" data-aos="fade-up">
        <div class="row justify-content-center">
          <div class="col-lg-8">
            <a href="index.php" class="back-link">
              <i class="bi bi-arrow-left"></i>Back to Home
            </a>

            <div class="form-card">
              <div class="form-header">
                <h2><i class="bi bi-plus-circle me-2"></i>Create New Room</h2>
                <p>Fill in your details to create a secure chat room</p>
              </div>

              <form method="post" id="createRoomForm">
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="name"><i class="bi bi-person me-2"></i>Your Name</label>
                      <input type="text" class="form-control" id="name" name="name" placeholder="Enter your full name" required>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="phone"><i class="bi bi-telephone me-2"></i>Phone Number</label>
                      <input type="tel" class="form-control" id="phone" name="phone" placeholder="e.g., +1234567890" required>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="age"><i class="bi bi-calendar me-2"></i>Age</label>
                      <input type="number" class="form-control" id="age" name="age" placeholder="Enter your age" min="13" max="100" required>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="gender"><i class="bi bi-gender-ambiguous me-2"></i>Gender</label>
                      <select class="form-select" id="gender" name="gender" required>
                        <option value="">Select Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <label for="password"><i class="bi bi-shield-lock me-2"></i>Room Password</label>
                  <input type="password" class="form-control" id="password" name="password" placeholder="Create a secure password" required>
                  <small class="text-muted">This password will be required for others to join your room</small>
                </div>

                <button type="submit" class="btn-create">
                  <i class="bi bi-rocket-takeoff me-2"></i>Create Room
                </button>
              </form>

              <div class="info-box">
                <h6><i class="bi bi-info-circle me-2"></i>What happens next?</h6>
                <p>After creating your room, you'll get a unique 6-digit room code. Share this code and password with friends to invite them to your chat room.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer id="footer" class="footer dark-background">
    <div class="container">
      <div class="row gy-3">
        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-geo-alt icon"></i>
          <div class="address">
            <h4>Address</h4>
            <p>QuickMeet HQ<br>Digital Communication Center<br></p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-telephone icon"></i>
          <div>
            <h4>Contact</h4>
            <p>
              <strong>Phone:</strong> <span>+91 7028844513</span><br>
              <strong>Email:</strong> <span><EMAIL></span><br>
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-clock icon"></i>
          <div>
            <h4>Available</h4>
            <p>
              <strong>24/7 Service</strong><br>
              Always online and ready to connect you with friends and family.
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6">
          <h4>Follow Us</h4>
          <div class="social-links d-flex">
            <a href="#" class="twitter"><i class="bi bi-twitter-x"></i></a>
            <a href="#" class="facebook"><i class="bi bi-facebook"></i></a>
            <a href="#" class="instagram"><i class="bi bi-instagram"></i></a>
            <a href="#" class="linkedin"><i class="bi bi-linkedin"></i></a>
          </div>
        </div>
      </div>
    </div>

    <div class="container copyright text-center mt-4">
      <p>© <span>Copyright</span> <strong class="px-1 sitename">QuickMeet</strong> <span>All Rights Reserved</span></p>
    </div>
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <script>
    // Form validation and enhancement
    document.getElementById('createRoomForm').addEventListener('submit', function(e) {
      const password = document.getElementById('password').value;
      if (password.length < 4) {
        e.preventDefault();
        alert('Password must be at least 4 characters long');
        return false;
      }
    });

    // Initialize AOS
    AOS.init();
  </script>

</body>
</html>