<?php
// Capture any output that might interfere with JSON
ob_start();

include 'db.php';

// Clean any output buffer and set JSON header
ob_clean();
header('Content-Type: application/json');

// Disable HTML error output for clean JSON responses
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Check database connection first
if (!$conn) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';

// Log the action for debugging (to file, not output)
error_log("Game Handler Action: " . $action);
error_log("POST data: " . print_r($_POST, true));
error_log("GET data: " . print_r($_GET, true));

try {
    // Ensure we have a valid action
    if (empty($action)) {
        echo json_encode(['success' => false, 'message' => 'No action specified']);
        exit;
    }

    switch ($action) {
        case 'create_game':
            createGame();
            break;
        case 'join_game':
            joinGame();
            break;
        case 'submit_number':
            submitNumber();
            break;
        case 'get_game_state':
            getGameState();
            break;
        case 'start_game':
            startGame();
            break;
        case 'leave_game':
            leaveGame();
            break;
        case 'check_break':
            checkBreakPeriod();
            break;
        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'Game handler is working!',
                'timestamp' => date('Y-m-d H:i:s'),
                'php_version' => PHP_VERSION,
                'action_received' => $action
            ]);
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action: ' . $action]);
    }
} catch (Exception $e) {
    error_log("Game Handler Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
} catch (Error $e) {
    error_log("Game Handler Fatal Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Fatal error: ' . $e->getMessage()]);
}

function createGame() {
    global $conn;

    $host_name = trim($_POST['host_name'] ?? '');
    $room_name = trim($_POST['room_name'] ?? '');

    error_log("Creating game: host=$host_name, room=$room_name");

    if (empty($host_name) || empty($room_name)) {
        echo json_encode(['success' => false, 'message' => 'Name and room name are required']);
        return;
    }

    // Check if game_rooms table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'game_rooms'");
    if ($table_check->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Game database not set up. Please run setup_game_db.php first.']);
        return;
    }

    // Generate unique room code
    do {
        $room_code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6));
        $check = $conn->prepare("SELECT room_code FROM game_rooms WHERE room_code = ?");
        if (!$check) {
            echo json_encode(['success' => false, 'message' => 'Database prepare error: ' . $conn->error]);
            return;
        }
        $check->bind_param("s", $room_code);
        $check->execute();
        $exists = $check->get_result()->num_rows > 0;
    } while ($exists);

    // Create game room
    $stmt = $conn->prepare("INSERT INTO game_rooms (room_code, room_name, host_name, status) VALUES (?, ?, ?, 'waiting')");
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database prepare error: ' . $conn->error]);
        return;
    }
    $stmt->bind_param("sss", $room_code, $room_name, $host_name);

    if ($stmt->execute()) {
        // Add host as first player
        $player_id = uniqid();
        $player_stmt = $conn->prepare("INSERT INTO game_players (room_code, player_name, player_id) VALUES (?, ?, ?)");
        if (!$player_stmt) {
            echo json_encode(['success' => false, 'message' => 'Database prepare error for player: ' . $conn->error]);
            return;
        }
        $player_stmt->bind_param("sss", $room_code, $host_name, $player_id);

        if ($player_stmt->execute()) {
            echo json_encode([
                'success' => true,
                'room_code' => $room_code,
                'host_name' => $host_name,
                'player_id' => $player_id
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to add player: ' . $player_stmt->error]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to create game room: ' . $stmt->error]);
    }
}

function joinGame() {
    global $conn;
    
    $room_code = trim($_POST['room_code'] ?? '');
    $player_name = trim($_POST['player_name'] ?? '');
    
    if (empty($room_code) || empty($player_name)) {
        echo json_encode(['success' => false, 'message' => 'Room code and name are required']);
        return;
    }
    
    // Check if room exists and is waiting
    $room_stmt = $conn->prepare("SELECT status FROM game_rooms WHERE room_code = ?");
    $room_stmt->bind_param("s", $room_code);
    $room_stmt->execute();
    $room_result = $room_stmt->get_result();
    
    if ($room_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Game room not found']);
        return;
    }
    
    $room = $room_result->fetch_assoc();
    if ($room['status'] !== 'waiting') {
        echo json_encode(['success' => false, 'message' => 'Game already started or finished']);
        return;
    }
    
    // Check player count
    $count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_players WHERE room_code = ? AND status = 'active'");
    $count_stmt->bind_param("s", $room_code);
    $count_stmt->execute();
    $count = $count_stmt->get_result()->fetch_assoc()['count'];
    
    if ($count >= 5) {
        echo json_encode(['success' => false, 'message' => 'Game room is full (5 players maximum)']);
        return;
    }
    
    // Check if player name already exists in room
    $name_stmt = $conn->prepare("SELECT player_name FROM game_players WHERE room_code = ? AND player_name = ?");
    $name_stmt->bind_param("ss", $room_code, $player_name);
    $name_stmt->execute();
    
    if ($name_stmt->get_result()->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'Player name already taken in this room']);
        return;
    }
    
    // Add player to game
    $player_id = uniqid();
    $stmt = $conn->prepare("INSERT INTO game_players (room_code, player_name, player_id) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $room_code, $player_name, $player_id);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'room_code' => $room_code,
            'player_name' => $player_name,
            'player_id' => $player_id
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to join game']);
    }
}

function startGame() {
    global $conn;

    $room_code = $_POST['room_code'] ?? '';
    $player_name = $_POST['player_name'] ?? '';

    // Check if room exists and is in waiting status
    $room_stmt = $conn->prepare("SELECT status FROM game_rooms WHERE room_code = ?");
    $room_stmt->bind_param("s", $room_code);
    $room_stmt->execute();
    $room_result = $room_stmt->get_result();

    if ($room_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Game room not found']);
        return;
    }

    $room = $room_result->fetch_assoc();
    if ($room['status'] !== 'waiting') {
        echo json_encode(['success' => false, 'message' => 'Game already started or finished']);
        return;
    }

    // Check if player is in the room
    $player_stmt = $conn->prepare("SELECT player_name FROM game_players WHERE room_code = ? AND player_name = ? AND status = 'active'");
    $player_stmt->bind_param("ss", $room_code, $player_name);
    $player_stmt->execute();

    if ($player_stmt->get_result()->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'You are not in this game room']);
        return;
    }

    // Check minimum players (2-5 players can play)
    $count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_players WHERE room_code = ? AND status = 'active'");
    $count_stmt->bind_param("s", $room_code);
    $count_stmt->execute();
    $count = $count_stmt->get_result()->fetch_assoc()['count'];

    if ($count < 2) {
        echo json_encode(['success' => false, 'message' => 'Need at least 2 players to start (2-5 players can play)']);
        return;
    }

    if ($count > 5) {
        echo json_encode(['success' => false, 'message' => 'Maximum 5 players allowed']);
        return;
    }

    // Start the game
    $stmt = $conn->prepare("UPDATE game_rooms SET status = 'playing', current_round = 1, round_start_time = NOW() WHERE room_code = ?");
    $stmt->bind_param("s", $room_code);

    if ($stmt->execute()) {
        // Skip chat message for game start to avoid foreign key issues
        // Game events are tracked in game_rooms and game_results tables instead

        echo json_encode(['success' => true, 'message' => 'Game started!']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to start game']);
    }
}

function submitNumber() {
    global $conn;

    $room_code = $_POST['room_code'] ?? '';
    $player_name = $_POST['player_name'] ?? '';
    $chosen_number = intval($_POST['chosen_number'] ?? -1);

    error_log("Submit Number: Room=$room_code, Player=$player_name, Number=$chosen_number");

    if ($chosen_number < 0 || $chosen_number > 100) {
        echo json_encode(['success' => false, 'message' => 'Number must be between 0 and 100']);
        return;
    }

    // Verify player exists and is active
    $player_check = $conn->prepare("SELECT player_name FROM game_players WHERE room_code = ? AND player_name = ? AND status = 'active'");
    $player_check->bind_param("ss", $room_code, $player_name);
    $player_check->execute();

    if ($player_check->get_result()->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Player not found or not active in this room']);
        return;
    }

    // Get current round
    $round_stmt = $conn->prepare("SELECT current_round, status FROM game_rooms WHERE room_code = ?");
    $round_stmt->bind_param("s", $room_code);
    $round_stmt->execute();
    $room_data = $round_stmt->get_result()->fetch_assoc();

    if (!$room_data || $room_data['status'] !== 'playing') {
        echo json_encode(['success' => false, 'message' => 'Game not active - current status: ' . ($room_data['status'] ?? 'unknown')]);
        return;
    }

    $current_round = $room_data['current_round'];

    // Check if player already submitted for this round
    $existing_check = $conn->prepare("SELECT chosen_number FROM game_rounds WHERE room_code = ? AND round_number = ? AND player_name = ? AND is_submitted = TRUE");
    $existing_check->bind_param("sis", $room_code, $current_round, $player_name);
    $existing_check->execute();

    if ($existing_check->get_result()->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'You have already submitted a number for this round']);
        return;
    }

    // Submit number for current round
    $stmt = $conn->prepare("INSERT INTO game_rounds (room_code, round_number, player_name, chosen_number, is_submitted) VALUES (?, ?, ?, ?, TRUE) ON DUPLICATE KEY UPDATE chosen_number = ?, is_submitted = TRUE");
    $stmt->bind_param("sisii", $room_code, $current_round, $player_name, $chosen_number, $chosen_number);

    if ($stmt->execute()) {
        error_log("Number submitted successfully for $player_name");

        // Skip chat message for submissions to avoid foreign key issues
        // Game submissions are tracked in game_rounds table instead

        // Do NOT check for round completion here - rounds must last full 60 seconds
        echo json_encode(['success' => true, 'message' => 'Number submitted successfully']);
    } else {
        error_log("Failed to submit number: " . $conn->error);
        echo json_encode(['success' => false, 'message' => 'Failed to submit number: ' . $conn->error]);
    }
}

function checkRoundComplete($room_code, $round_number) {
    global $conn;

    // Get round start time
    $time_stmt = $conn->prepare("SELECT round_start_time FROM game_rooms WHERE room_code = ?");
    $time_stmt->bind_param("s", $room_code);
    $time_stmt->execute();
    $time_result = $time_stmt->get_result()->fetch_assoc();

    if (!$time_result || !$time_result['round_start_time']) {
        return;
    }

    $round_start = strtotime($time_result['round_start_time']);
    $current_time = time();
    $elapsed_time = $current_time - $round_start;

    // Count active players
    $active_stmt = $conn->prepare("SELECT player_name FROM game_players WHERE room_code = ? AND status = 'active'");
    $active_stmt->bind_param("s", $room_code);
    $active_stmt->execute();
    $active_players = $active_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $active_count = count($active_players);

    // Count submitted numbers for this round
    $submitted_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_rounds WHERE room_code = ? AND round_number = ? AND is_submitted = TRUE");
    $submitted_stmt->bind_param("si", $room_code, $round_number);
    $submitted_stmt->execute();
    $submitted_count = $submitted_stmt->get_result()->fetch_assoc()['count'];

    // If 60 seconds (1 minute) passed, auto-submit random numbers for players who didn't submit
    if ($elapsed_time >= 60) {
        autoSubmitMissingNumbers($room_code, $round_number, $active_players);
        calculateRoundResults($room_code, $round_number);
        return;
    }

    // Do NOT end round early even if all players submitted
    // Round must always last the full 60 seconds (1 minute)
    // Results will only be calculated when time expires
}

function autoSubmitMissingNumbers($room_code, $round_number, $active_players) {
    global $conn;

    foreach ($active_players as $player) {
        $player_name = $player['player_name'];

        // Check if player already submitted
        $check_stmt = $conn->prepare("SELECT id FROM game_rounds WHERE room_code = ? AND round_number = ? AND player_name = ? AND is_submitted = TRUE");
        $check_stmt->bind_param("sis", $room_code, $round_number, $player_name);
        $check_stmt->execute();

        if ($check_stmt->get_result()->num_rows === 0) {
            // Player didn't submit, auto-submit random number
            $random_number = rand(0, 100);

            $auto_stmt = $conn->prepare("INSERT INTO game_rounds (room_code, round_number, player_name, chosen_number, is_submitted) VALUES (?, ?, ?, ?, TRUE) ON DUPLICATE KEY UPDATE chosen_number = ?, is_submitted = TRUE");
            $auto_stmt->bind_param("sisii", $room_code, $round_number, $player_name, $random_number, $random_number);
            $auto_stmt->execute();

            // Skip chat message for auto-submission to avoid foreign key issues
            // Auto-submissions are tracked in game_rounds table instead
        }
    }
}

function calculateRoundResults($room_code, $round_number) {
    global $conn;
    
    // Get all submitted numbers for this round
    $numbers_stmt = $conn->prepare("SELECT player_name, chosen_number FROM game_rounds WHERE room_code = ? AND round_number = ? AND is_submitted = TRUE");
    $numbers_stmt->bind_param("si", $room_code, $round_number);
    $numbers_stmt->execute();
    $numbers_result = $numbers_stmt->get_result();
    
    $numbers = [];
    $players = [];
    
    while ($row = $numbers_result->fetch_assoc()) {
        $numbers[] = intval($row['chosen_number']);
        $players[$row['player_name']] = intval($row['chosen_number']);
    }

    if (empty($numbers)) {
        return;
    }

    // Get current active player count to determine which rules apply
    $active_count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_players WHERE room_code = ? AND status = 'active'");
    $active_count_stmt->bind_param("s", $room_code);
    $active_count_stmt->execute();
    $active_count = $active_count_stmt->get_result()->fetch_assoc()['count'];

    // Progressive rules based on player count
    $rule1_active = ($active_count <= 4); // Same number disqualification
    $rule2_active = ($active_count <= 3); // Loser gets -2 instead of -1
    $rule3_active = ($active_count <= 2); // 0 vs 100 special rule

    $disqualified_players = [];
    $special_winner = null;
    $rule_messages = [];

    // RULE 1: Check for duplicate numbers (4 players or less)
    if ($rule1_active) {
        $number_counts = array_count_values($numbers);
        foreach ($number_counts as $number => $count) {
            if ($count > 1) {
                // Find all players who chose this duplicate number
                $duplicate_players = [];
                foreach ($players as $player_name => $chosen_number) {
                    if ($chosen_number == $number) {
                        $disqualified_players[] = $player_name;
                        $duplicate_players[] = $player_name;
                    }
                }
                $rule_messages[] = "🚫 Rule 1: " . implode(", ", $duplicate_players) . " chose same number ($number) - Disqualified!";
            }
        }
    }

    // RULE 3: Special 0 vs 100 rule (2 players only)
    if ($rule3_active && $active_count == 2) {
        $player_names = array_keys($players);
        $player1_number = $players[$player_names[0]];
        $player2_number = $players[$player_names[1]];

        if ($player1_number == 0 && $player2_number == 100) {
            $special_winner = $player_names[1]; // Player with 100 wins
            $rule_messages[] = "⚡ Rule 3: " . $player_names[0] . " chose 0, " . $player_names[1] . " chose 100 - " . $player_names[1] . " wins!";
        } elseif ($player2_number == 0 && $player1_number == 100) {
            $special_winner = $player_names[0]; // Player with 100 wins
            $rule_messages[] = "⚡ Rule 3: " . $player_names[1] . " chose 0, " . $player_names[0] . " chose 100 - " . $player_names[0] . " wins!";
        }
    }

    // Calculate average and target (works with 2-5 players)
    $player_count = count($numbers);
    $average = array_sum($numbers) / $player_count;
    $target = $average * 0.8;
    
    // Determine winner
    $winner = null;

    if ($special_winner) {
        // Rule 3 special winner (0 vs 100)
        $winner = $special_winner;
    } else {
        // Standard winner calculation (closest to target)
        $min_distance = PHP_FLOAT_MAX;
        foreach ($players as $player_name => $number) {
            // Skip disqualified players
            if (in_array($player_name, $disqualified_players)) {
                continue;
            }

            $distance = abs($number - $target);
            if ($distance < $min_distance) {
                $min_distance = $distance;
                $winner = $player_name;
            }
        }
    }

    // Update scores with progressive rules
    foreach ($players as $player_name => $number) {
        $score_change = 0;

        if (in_array($player_name, $disqualified_players)) {
            // Rule 1: Disqualified players lose -1 point
            $score_change = -1;
        } elseif ($player_name === $winner) {
            // Winner gets +1 point
            $score_change = 1;
        } else {
            // Losers get penalty based on Rule 2
            if ($rule2_active) {
                // Rule 2: Loser gets -2 instead of -1 (3 players or less)
                $score_change = -2;
            } else {
                // Standard: Loser gets -1
                $score_change = -1;
            }
        }

        $update_stmt = $conn->prepare("UPDATE game_players SET score = score + ? WHERE room_code = ? AND player_name = ?");
        $update_stmt->bind_param("iss", $score_change, $room_code, $player_name);
        $update_stmt->execute();
    }
    
    // Check for eliminations (score <= -10)
    $eliminate_stmt = $conn->prepare("UPDATE game_players SET status = 'eliminated' WHERE room_code = ? AND score <= -10 AND status = 'active'");
    $eliminate_stmt->bind_param("s", $room_code);
    $eliminate_stmt->execute();
    
    // Save round results with rule information
    $rules_applied = [];
    if ($rule1_active) $rules_applied[] = "Rule1(Duplicates)";
    if ($rule2_active) $rules_applied[] = "Rule2(-2Points)";
    if ($rule3_active) $rules_applied[] = "Rule3(0vs100)";
    $rules_text = implode(", ", $rules_applied);
    $rule_messages_text = empty($rule_messages) ? NULL : implode(" | ", $rule_messages);

    // Try to insert with rule information, fallback if columns don't exist
    try {
        $result_stmt = $conn->prepare("INSERT INTO game_results (room_code, round_number, average_number, target_number, winner_name, rules_applied, rule_messages) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $result_stmt->bind_param("siddss", $room_code, $round_number, $average, $target, $winner, $rules_text, $rule_messages_text);
        $result_stmt->execute();
    } catch (Exception $e) {
        // Fallback for older database schema
        $result_stmt = $conn->prepare("INSERT INTO game_results (room_code, round_number, average_number, target_number, winner_name) VALUES (?, ?, ?, ?, ?)");
        $result_stmt->bind_param("sidds", $room_code, $round_number, $average, $target, $winner);
        $result_stmt->execute();
    }
    
    // Check if game should end (only 1 active player left)
    $active_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_players WHERE room_code = ? AND status = 'active'");
    $active_stmt->bind_param("s", $room_code);
    $active_stmt->execute();
    $active_count = $active_stmt->get_result()->fetch_assoc()['count'];
    
    if ($active_count <= 1) {
        // Game over
        $end_stmt = $conn->prepare("UPDATE game_rooms SET status = 'finished' WHERE room_code = ?");
        $end_stmt->bind_param("s", $room_code);
        $end_stmt->execute();

        // Skip chat message for game end to avoid foreign key issues
        // Game results are tracked in game_results and game_players tables instead
    } else {
        // Set room to 'break' status for 30 seconds to show results
        $break_stmt = $conn->prepare("UPDATE game_rooms SET status = 'break', round_start_time = NOW() WHERE room_code = ?");
        $break_stmt->bind_param("s", $room_code);
        $break_stmt->execute();

        // Skip chat message for round results to avoid foreign key issues
        // Round results are displayed in the game interface instead
    }
}

function getGameState() {
    global $conn;
    
    $room_code = $_GET['room_code'] ?? '';
    
    // Get room info
    $room_stmt = $conn->prepare("SELECT * FROM game_rooms WHERE room_code = ?");
    $room_stmt->bind_param("s", $room_code);
    $room_stmt->execute();
    $room = $room_stmt->get_result()->fetch_assoc();
    
    if (!$room) {
        echo json_encode(['success' => false, 'message' => 'Room not found']);
        return;
    }
    
    // Get players
    $players_stmt = $conn->prepare("SELECT player_name, score, status FROM game_players WHERE room_code = ? ORDER BY score DESC, player_name");
    $players_stmt->bind_param("s", $room_code);
    $players_stmt->execute();
    $players = $players_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Get current round submissions
    $submissions = [];
    if ($room['status'] === 'playing') {
        $sub_stmt = $conn->prepare("SELECT player_name, is_submitted FROM game_rounds WHERE room_code = ? AND round_number = ?");
        $sub_stmt->bind_param("si", $room_code, $room['current_round']);
        $sub_stmt->execute();
        $sub_result = $sub_stmt->get_result();
        while ($row = $sub_result->fetch_assoc()) {
            $submissions[$row['player_name']] = $row['is_submitted'];
        }
    }
    
    // Get last round results with player choices
    $last_result = null;
    $last_round_choices = [];
    if ($room['current_round'] > 1 || $room['status'] === 'break') {
        $last_round = $room['status'] === 'break' ? $room['current_round'] : $room['current_round'] - 1;

        // Get round results
        $result_stmt = $conn->prepare("SELECT * FROM game_results WHERE room_code = ? AND round_number = ? ORDER BY calculated_at DESC LIMIT 1");
        $result_stmt->bind_param("si", $room_code, $last_round);
        $result_stmt->execute();
        $last_result = $result_stmt->get_result()->fetch_assoc();

        // Get all player choices for the last round
        $choices_stmt = $conn->prepare("SELECT player_name, chosen_number FROM game_rounds WHERE room_code = ? AND round_number = ? AND is_submitted = TRUE ORDER BY player_name");
        $choices_stmt->bind_param("si", $room_code, $last_round);
        $choices_stmt->execute();
        $choices_result = $choices_stmt->get_result();

        while ($choice = $choices_result->fetch_assoc()) {
            $last_round_choices[] = $choice;
        }
    }
    
    // Calculate time remaining
    $time_remaining = 60;
    $break_remaining = 0;

    if ($room['status'] === 'playing' && $room['round_start_time']) {
        $start_time = strtotime($room['round_start_time']);
        $elapsed = time() - $start_time;
        $time_remaining = max(0, 60 - $elapsed);

        // Auto-check for round completion if time is up
        if ($time_remaining === 0) {
            checkRoundComplete($room_code, $room['current_round']);
        }
    } elseif ($room['status'] === 'break' && $room['round_start_time']) {
        $break_start = strtotime($room['round_start_time']);
        $elapsed = time() - $break_start;
        $break_remaining = max(0, 30 - $elapsed);

        // Auto-start next round if break is over
        if ($break_remaining === 0) {
            checkBreakPeriod();
        }
    }
    
    echo json_encode([
        'success' => true,
        'room' => $room,
        'players' => $players,
        'submissions' => $submissions,
        'last_result' => $last_result,
        'last_round_choices' => $last_round_choices,
        'time_remaining' => $time_remaining,
        'break_remaining' => $break_remaining
    ]);
}

function checkBreakPeriod() {
    global $conn;

    $room_code = $_GET['room_code'] ?? '';

    if (empty($room_code)) {
        echo json_encode(['success' => false, 'message' => 'Room code required']);
        return;
    }

    // Get rooms in break status
    $break_stmt = $conn->prepare("SELECT current_round, round_start_time FROM game_rooms WHERE room_code = ? AND status = 'break'");
    $break_stmt->bind_param("s", $room_code);
    $break_stmt->execute();
    $break_result = $break_stmt->get_result();

    if ($break_result->num_rows === 0) {
        echo json_encode(['success' => true, 'message' => 'No break period active']);
        return;
    }

    $room = $break_result->fetch_assoc();
    $break_start = strtotime($room['round_start_time']);
    $current_time = time();
    $elapsed_time = $current_time - $break_start;

    // If 30 seconds passed, start next round (keep break at 30 seconds)
    if ($elapsed_time >= 30) {
        $next_round = $room['current_round'] + 1;
        $next_stmt = $conn->prepare("UPDATE game_rooms SET status = 'playing', current_round = ?, round_start_time = NOW() WHERE room_code = ?");
        $next_stmt->bind_param("is", $next_round, $room_code);
        $next_stmt->execute();

        // Skip chat message for new round to avoid foreign key issues
        // Round information is displayed in the game interface instead

        echo json_encode(['success' => true, 'message' => 'Next round started', 'new_round' => $next_round]);
    } else {
        $remaining = 30 - $elapsed_time;
        echo json_encode(['success' => true, 'message' => 'Break period active', 'remaining' => $remaining]);
    }
}

function leaveGame() {
    global $conn;

    $room_code = $_POST['room_code'] ?? '';
    $player_name = $_POST['player_name'] ?? '';

    $stmt = $conn->prepare("UPDATE game_players SET status = 'disconnected' WHERE room_code = ? AND player_name = ?");
    $stmt->bind_param("ss", $room_code, $player_name);

    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to leave game']);
    }
}
?>
