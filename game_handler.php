<?php
include 'db.php';
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

$action = $_POST['action'] ?? $_GET['action'] ?? '';

// Log the action for debugging
error_log("Game Handler Action: " . $action);

try {
    switch ($action) {
        case 'create_game':
            createGame();
            break;
        case 'join_game':
            joinGame();
            break;
        case 'submit_number':
            submitNumber();
            break;
        case 'get_game_state':
            getGameState();
            break;
        case 'start_game':
            startGame();
            break;
        case 'leave_game':
            leaveGame();
            break;
        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'Game handler is working!',
                'timestamp' => date('Y-m-d H:i:s'),
                'php_version' => PHP_VERSION
            ]);
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action: ' . $action]);
    }
} catch (Exception $e) {
    error_log("Game Handler Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function createGame() {
    global $conn;

    $host_name = trim($_POST['host_name'] ?? '');
    $room_name = trim($_POST['room_name'] ?? '');

    error_log("Creating game: host=$host_name, room=$room_name");

    if (empty($host_name) || empty($room_name)) {
        echo json_encode(['success' => false, 'message' => 'Name and room name are required']);
        return;
    }

    // Check if game_rooms table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'game_rooms'");
    if ($table_check->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Game database not set up. Please run setup_game_db.php first.']);
        return;
    }

    // Generate unique room code
    do {
        $room_code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6));
        $check = $conn->prepare("SELECT room_code FROM game_rooms WHERE room_code = ?");
        if (!$check) {
            echo json_encode(['success' => false, 'message' => 'Database prepare error: ' . $conn->error]);
            return;
        }
        $check->bind_param("s", $room_code);
        $check->execute();
        $exists = $check->get_result()->num_rows > 0;
    } while ($exists);

    // Create game room
    $stmt = $conn->prepare("INSERT INTO game_rooms (room_code, room_name, host_name, status) VALUES (?, ?, ?, 'waiting')");
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database prepare error: ' . $conn->error]);
        return;
    }
    $stmt->bind_param("sss", $room_code, $room_name, $host_name);

    if ($stmt->execute()) {
        // Add host as first player
        $player_id = uniqid();
        $player_stmt = $conn->prepare("INSERT INTO game_players (room_code, player_name, player_id) VALUES (?, ?, ?)");
        if (!$player_stmt) {
            echo json_encode(['success' => false, 'message' => 'Database prepare error for player: ' . $conn->error]);
            return;
        }
        $player_stmt->bind_param("sss", $room_code, $host_name, $player_id);

        if ($player_stmt->execute()) {
            echo json_encode([
                'success' => true,
                'room_code' => $room_code,
                'host_name' => $host_name,
                'player_id' => $player_id
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to add player: ' . $player_stmt->error]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to create game room: ' . $stmt->error]);
    }
}

function joinGame() {
    global $conn;
    
    $room_code = trim($_POST['room_code'] ?? '');
    $player_name = trim($_POST['player_name'] ?? '');
    
    if (empty($room_code) || empty($player_name)) {
        echo json_encode(['success' => false, 'message' => 'Room code and name are required']);
        return;
    }
    
    // Check if room exists and is waiting
    $room_stmt = $conn->prepare("SELECT status FROM game_rooms WHERE room_code = ?");
    $room_stmt->bind_param("s", $room_code);
    $room_stmt->execute();
    $room_result = $room_stmt->get_result();
    
    if ($room_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Game room not found']);
        return;
    }
    
    $room = $room_result->fetch_assoc();
    if ($room['status'] !== 'waiting') {
        echo json_encode(['success' => false, 'message' => 'Game already started or finished']);
        return;
    }
    
    // Check player count
    $count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_players WHERE room_code = ? AND status = 'active'");
    $count_stmt->bind_param("s", $room_code);
    $count_stmt->execute();
    $count = $count_stmt->get_result()->fetch_assoc()['count'];
    
    if ($count >= 5) {
        echo json_encode(['success' => false, 'message' => 'Game room is full (5 players maximum)']);
        return;
    }
    
    // Check if player name already exists in room
    $name_stmt = $conn->prepare("SELECT player_name FROM game_players WHERE room_code = ? AND player_name = ?");
    $name_stmt->bind_param("ss", $room_code, $player_name);
    $name_stmt->execute();
    
    if ($name_stmt->get_result()->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'Player name already taken in this room']);
        return;
    }
    
    // Add player to game
    $player_id = uniqid();
    $stmt = $conn->prepare("INSERT INTO game_players (room_code, player_name, player_id) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $room_code, $player_name, $player_id);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'room_code' => $room_code,
            'player_name' => $player_name,
            'player_id' => $player_id
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to join game']);
    }
}

function startGame() {
    global $conn;

    $room_code = $_POST['room_code'] ?? '';
    $player_name = $_POST['player_name'] ?? '';

    // Check if room exists and is in waiting status
    $room_stmt = $conn->prepare("SELECT status FROM game_rooms WHERE room_code = ?");
    $room_stmt->bind_param("s", $room_code);
    $room_stmt->execute();
    $room_result = $room_stmt->get_result();

    if ($room_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Game room not found']);
        return;
    }

    $room = $room_result->fetch_assoc();
    if ($room['status'] !== 'waiting') {
        echo json_encode(['success' => false, 'message' => 'Game already started or finished']);
        return;
    }

    // Check if player is in the room
    $player_stmt = $conn->prepare("SELECT player_name FROM game_players WHERE room_code = ? AND player_name = ? AND status = 'active'");
    $player_stmt->bind_param("ss", $room_code, $player_name);
    $player_stmt->execute();

    if ($player_stmt->get_result()->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'You are not in this game room']);
        return;
    }

    // Check minimum players (at least 2 for testing, 5 for full game)
    $count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_players WHERE room_code = ? AND status = 'active'");
    $count_stmt->bind_param("s", $room_code);
    $count_stmt->execute();
    $count = $count_stmt->get_result()->fetch_assoc()['count'];

    if ($count < 2) {
        echo json_encode(['success' => false, 'message' => 'Need at least 2 players to start']);
        return;
    }

    // Start the game
    $stmt = $conn->prepare("UPDATE game_rooms SET status = 'playing', current_round = 1, round_start_time = NOW() WHERE room_code = ?");
    $stmt->bind_param("s", $room_code);

    if ($stmt->execute()) {
        // Add system message about game start
        $msg_stmt = $conn->prepare("INSERT INTO messages (room_code, sender_name, message, game_room, message_type) VALUES (?, 'System', ?, TRUE, 'system')");
        $start_msg = "🎮 Game started by " . $player_name . "! Round 1 begins now!";
        $msg_stmt->bind_param("ss", $room_code, $start_msg);
        $msg_stmt->execute();

        echo json_encode(['success' => true, 'message' => 'Game started!']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to start game']);
    }
}

function submitNumber() {
    global $conn;
    
    $room_code = $_POST['room_code'] ?? '';
    $player_name = $_POST['player_name'] ?? '';
    $chosen_number = intval($_POST['chosen_number'] ?? -1);
    
    if ($chosen_number < 0 || $chosen_number > 100) {
        echo json_encode(['success' => false, 'message' => 'Number must be between 0 and 100']);
        return;
    }
    
    // Get current round
    $round_stmt = $conn->prepare("SELECT current_round, status FROM game_rooms WHERE room_code = ?");
    $round_stmt->bind_param("s", $room_code);
    $round_stmt->execute();
    $room_data = $round_stmt->get_result()->fetch_assoc();
    
    if (!$room_data || $room_data['status'] !== 'playing') {
        echo json_encode(['success' => false, 'message' => 'Game not active']);
        return;
    }
    
    $current_round = $room_data['current_round'];
    
    // Submit number for current round
    $stmt = $conn->prepare("INSERT INTO game_rounds (room_code, round_number, player_name, chosen_number, is_submitted) VALUES (?, ?, ?, ?, TRUE) ON DUPLICATE KEY UPDATE chosen_number = ?, is_submitted = TRUE");
    $stmt->bind_param("sisii", $room_code, $current_round, $player_name, $chosen_number, $chosen_number);
    
    if ($stmt->execute()) {
        // Check if all active players have submitted
        checkRoundComplete($room_code, $current_round);
        echo json_encode(['success' => true, 'message' => 'Number submitted']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to submit number']);
    }
}

function checkRoundComplete($room_code, $round_number) {
    global $conn;
    
    // Count active players
    $active_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_players WHERE room_code = ? AND status = 'active'");
    $active_stmt->bind_param("s", $room_code);
    $active_stmt->execute();
    $active_count = $active_stmt->get_result()->fetch_assoc()['count'];
    
    // Count submitted numbers for this round
    $submitted_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_rounds WHERE room_code = ? AND round_number = ? AND is_submitted = TRUE");
    $submitted_stmt->bind_param("si", $room_code, $round_number);
    $submitted_stmt->execute();
    $submitted_count = $submitted_stmt->get_result()->fetch_assoc()['count'];
    
    // If all active players submitted, calculate results
    if ($active_count === $submitted_count) {
        calculateRoundResults($room_code, $round_number);
    }
}

function calculateRoundResults($room_code, $round_number) {
    global $conn;
    
    // Get all submitted numbers for this round
    $numbers_stmt = $conn->prepare("SELECT player_name, chosen_number FROM game_rounds WHERE room_code = ? AND round_number = ? AND is_submitted = TRUE");
    $numbers_stmt->bind_param("si", $room_code, $round_number);
    $numbers_stmt->execute();
    $numbers_result = $numbers_stmt->get_result();
    
    $numbers = [];
    $players = [];
    
    while ($row = $numbers_result->fetch_assoc()) {
        $numbers[] = $row['chosen_number'];
        $players[$row['player_name']] = $row['chosen_number'];
    }
    
    // Calculate average and target
    $average = array_sum($numbers) / count($numbers);
    $target = $average * 0.8;
    
    // Find winner (closest to target)
    $winner = null;
    $min_distance = PHP_FLOAT_MAX;
    
    foreach ($players as $player_name => $number) {
        $distance = abs($number - $target);
        if ($distance < $min_distance) {
            $min_distance = $distance;
            $winner = $player_name;
        }
    }
    
    // Update scores
    foreach ($players as $player_name => $number) {
        $score_change = ($player_name === $winner) ? 1 : -1;
        $update_stmt = $conn->prepare("UPDATE game_players SET score = score + ? WHERE room_code = ? AND player_name = ?");
        $update_stmt->bind_param("iss", $score_change, $room_code, $player_name);
        $update_stmt->execute();
    }
    
    // Check for eliminations (score <= -10)
    $eliminate_stmt = $conn->prepare("UPDATE game_players SET status = 'eliminated' WHERE room_code = ? AND score <= -10 AND status = 'active'");
    $eliminate_stmt->bind_param("s", $room_code);
    $eliminate_stmt->execute();
    
    // Save round results
    $result_stmt = $conn->prepare("INSERT INTO game_results (room_code, round_number, average_number, target_number, winner_name) VALUES (?, ?, ?, ?, ?)");
    $result_stmt->bind_param("sidds", $room_code, $round_number, $average, $target, $winner);
    $result_stmt->execute();
    
    // Check if game should end (only 1 active player left)
    $active_stmt = $conn->prepare("SELECT COUNT(*) as count FROM game_players WHERE room_code = ? AND status = 'active'");
    $active_stmt->bind_param("s", $room_code);
    $active_stmt->execute();
    $active_count = $active_stmt->get_result()->fetch_assoc()['count'];
    
    if ($active_count <= 1) {
        // Game over
        $end_stmt = $conn->prepare("UPDATE game_rooms SET status = 'finished' WHERE room_code = ?");
        $end_stmt->bind_param("s", $room_code);
        $end_stmt->execute();

        // Add system message about game end
        $winner_stmt = $conn->prepare("SELECT player_name FROM game_players WHERE room_code = ? AND status = 'active' LIMIT 1");
        $winner_stmt->bind_param("s", $room_code);
        $winner_stmt->execute();
        $winner_result = $winner_stmt->get_result();

        if ($winner_result->num_rows > 0) {
            $winner_name = $winner_result->fetch_assoc()['player_name'];
            $msg_stmt = $conn->prepare("INSERT INTO messages (room_code, sender_name, message, game_room, message_type) VALUES (?, 'System', ?, TRUE, 'system')");
            $game_end_msg = "🎉 Game Over! Winner: " . $winner_name . " 🏆";
            $msg_stmt->bind_param("ss", $room_code, $game_end_msg);
            $msg_stmt->execute();
        }
    } else {
        // Start next round
        $next_round = $round_number + 1;
        $next_stmt = $conn->prepare("UPDATE game_rooms SET current_round = ?, round_start_time = NOW() WHERE room_code = ?");
        $next_stmt->bind_param("is", $next_round, $room_code);
        $next_stmt->execute();

        // Add system message about round results
        $msg_stmt = $conn->prepare("INSERT INTO messages (room_code, sender_name, message, game_room, message_type) VALUES (?, 'System', ?, TRUE, 'system')");
        $round_msg = "🎯 Round $round_number Results: Average: " . number_format($average, 2) . " | Target: " . number_format($target, 2) . " | Winner: $winner (+1 point)";
        $msg_stmt->bind_param("ss", $room_code, $round_msg);
        $msg_stmt->execute();
    }
}

function getGameState() {
    global $conn;
    
    $room_code = $_GET['room_code'] ?? '';
    
    // Get room info
    $room_stmt = $conn->prepare("SELECT * FROM game_rooms WHERE room_code = ?");
    $room_stmt->bind_param("s", $room_code);
    $room_stmt->execute();
    $room = $room_stmt->get_result()->fetch_assoc();
    
    if (!$room) {
        echo json_encode(['success' => false, 'message' => 'Room not found']);
        return;
    }
    
    // Get players
    $players_stmt = $conn->prepare("SELECT player_name, score, status FROM game_players WHERE room_code = ? ORDER BY score DESC, player_name");
    $players_stmt->bind_param("s", $room_code);
    $players_stmt->execute();
    $players = $players_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Get current round submissions
    $submissions = [];
    if ($room['status'] === 'playing') {
        $sub_stmt = $conn->prepare("SELECT player_name, is_submitted FROM game_rounds WHERE room_code = ? AND round_number = ?");
        $sub_stmt->bind_param("si", $room_code, $room['current_round']);
        $sub_stmt->execute();
        $sub_result = $sub_stmt->get_result();
        while ($row = $sub_result->fetch_assoc()) {
            $submissions[$row['player_name']] = $row['is_submitted'];
        }
    }
    
    // Get last round results
    $last_result = null;
    if ($room['current_round'] > 1) {
        $result_stmt = $conn->prepare("SELECT * FROM game_results WHERE room_code = ? AND round_number = ? ORDER BY calculated_at DESC LIMIT 1");
        $last_round = $room['current_round'] - 1;
        $result_stmt->bind_param("si", $room_code, $last_round);
        $result_stmt->execute();
        $last_result = $result_stmt->get_result()->fetch_assoc();
    }
    
    // Calculate time remaining
    $time_remaining = 30;
    if ($room['status'] === 'playing' && $room['round_start_time']) {
        $start_time = strtotime($room['round_start_time']);
        $elapsed = time() - $start_time;
        $time_remaining = max(0, 30 - $elapsed);
    }
    
    echo json_encode([
        'success' => true,
        'room' => $room,
        'players' => $players,
        'submissions' => $submissions,
        'last_result' => $last_result,
        'time_remaining' => $time_remaining
    ]);
}

function leaveGame() {
    global $conn;
    
    $room_code = $_POST['room_code'] ?? '';
    $player_name = $_POST['player_name'] ?? '';
    
    $stmt = $conn->prepare("UPDATE game_players SET status = 'disconnected' WHERE room_code = ? AND player_name = ?");
    $stmt->bind_param("ss", $room_code, $player_name);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to leave game']);
    }
}
?>
