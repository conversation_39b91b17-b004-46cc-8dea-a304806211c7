<?php include 'db.php'; ?>
<?php
$error_message = '';
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $room_code = $_POST['room_code'];
    $password = $_POST['password'];
    $name = $_POST['name'];

    $result = $conn->query("SELECT * FROM rooms WHERE room_code = '$room_code' AND password = '$password'");
    if ($result->num_rows > 0) {
        header("Location: room.php?code=$room_code&name=" . urlencode($name));
        exit;
    } else {
        $error_message = "Invalid room code or password. Please check and try again.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Join Room - QuickMeet</title>
  <meta name="description" content="Join an existing chat room using your room code and password.">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    .form-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      padding: 120px 0 80px 0;
    }

    .form-card {
      background: var(--surface-color);
      border-radius: 20px;
      padding: 3rem;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(206, 175, 127, 0.1);
      max-width: 500px;
      margin: 0 auto;
    }

    .form-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .form-header h2 {
      color: var(--heading-color);
      margin-bottom: 0.5rem;
    }

    .form-header p {
      color: var(--default-color);
      opacity: 0.8;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      color: var(--heading-color);
      font-weight: 500;
    }

    .form-control {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 10px;
      padding: 12px 16px;
      color: var(--default-color);
      font-size: 1rem;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      background: rgba(255, 255, 255, 0.1);
      border-color: var(--accent-color);
      box-shadow: 0 0 0 0.2rem rgba(206, 175, 127, 0.25);
      color: var(--default-color);
    }

    .form-control::placeholder {
      color: rgba(241, 243, 245, 0.6);
    }

    .btn-join {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 15px 40px;
      border-radius: 50px;
      border: none;
      font-weight: 600;
      font-size: 1.1rem;
      width: 100%;
      transition: all 0.3s ease;
      margin-top: 1rem;
    }

    .btn-join:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 30px rgba(206, 175, 127, 0.3);
      color: var(--contrast-color);
    }

    .back-link {
      color: var(--accent-color);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      margin-bottom: 2rem;
      transition: color 0.3s ease;
    }

    .back-link:hover {
      color: #d4b896;
    }

    .back-link i {
      margin-right: 0.5rem;
    }

    .error-alert {
      background: rgba(220, 53, 69, 0.1);
      border: 1px solid rgba(220, 53, 69, 0.3);
      color: #ff6b7a;
      border-radius: 10px;
      padding: 1rem;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
    }

    .error-alert i {
      margin-right: 0.5rem;
      font-size: 1.2rem;
    }

    .help-section {
      background: rgba(206, 175, 127, 0.1);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 10px;
      padding: 1.5rem;
      margin-top: 2rem;
      text-align: center;
    }

    .help-section h6 {
      color: var(--accent-color);
      margin-bottom: 1rem;
    }

    .help-section p {
      margin-bottom: 1rem;
      opacity: 0.8;
    }

    .btn-create-alt {
      background: transparent;
      border: 2px solid var(--accent-color);
      color: var(--accent-color);
      padding: 10px 30px;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-create-alt:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .room-code-input {
      text-align: center;
      font-size: 1.5rem;
      font-weight: 600;
      letter-spacing: 0.2em;
      text-transform: uppercase;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .input-group {
      display: flex;
      align-items: stretch;
    }

    .btn-paste {
      background: var(--accent-color);
      color: var(--contrast-color);
      border: 1px solid var(--accent-color);
      border-left: none;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
      padding: 12px 16px;
      transition: all 0.3s ease;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 50px;
    }

    .btn-paste:hover {
      background: #d4b896;
      transform: scale(1.05);
    }

    .btn-paste:active {
      transform: scale(0.95);
    }
  </style>
</head>

<body>

  <!-- Header -->
  <header id="header" class="header d-flex align-items-center sticky-top">
    <div class="container position-relative d-flex align-items-center justify-content-between">

      <a href="index.php" class="logo d-flex align-items-center me-auto me-xl-0">
        <img src="assets/img/chatbot-logo.png" alt="QuickMeet Logo" style="height: 40px; margin-right: 0.5rem;">
        <h1 class="sitename">QuickMeet</h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.php">Home</a></li>
          <li><a href="index.php#features">Features</a></li>
          <li><a href="index.php#how-it-works">How It Works</a></li>
          <li><a href="https://quickf.free.nf/" target="_blank">Quick</a></li>
          <li><a href="admin.php">Admin</a></li>
          <li><a href="create_room.php">Create Room</a></li>
          <li><a href="join_room.php" class="active">Join Room</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

    </div>
  </header>

  <main class="main">
    <section class="form-container">
      <div class="container" data-aos="fade-up">
        <div class="row justify-content-center">
          <div class="col-lg-6">
            <a href="index.php" class="back-link">
              <i class="bi bi-arrow-left"></i>Back to Home
            </a>

            <div class="form-card">
              <div class="form-header">
                <h2><i class="bi bi-door-open me-2"></i>Join Room</h2>
                <p>Enter the room code and password to join the conversation</p>
              </div>

              <?php if ($error_message): ?>
                <div class="error-alert">
                  <i class="bi bi-exclamation-triangle"></i>
                  <?= htmlspecialchars($error_message) ?>
                </div>
              <?php endif; ?>

              <form method="post" id="joinRoomForm">
                <div class="form-group">
                  <label for="room_code"><i class="bi bi-key me-2"></i>Room Code</label>
                  <div class="input-group">
                    <input type="text" class="form-control room-code-input" id="room_code" name="room_code"
                           placeholder="123456" maxlength="6" pattern="[0-9]{6}"
                           value="<?= isset($_POST['room_code']) ? htmlspecialchars($_POST['room_code']) : '' ?>" required>
                    <button type="button" class="btn-paste" onclick="pasteRoomCode()" title="Paste room code">
                      <i class="bi bi-clipboard"></i>
                    </button>
                  </div>
                  <small class="text-muted">Enter the 6-digit room code or click paste to use clipboard</small>
                </div>

                <div class="form-group">
                  <label for="name"><i class="bi bi-person me-2"></i>Your Name</label>
                  <input type="text" class="form-control" id="name" name="name"
                         placeholder="Enter your name"
                         value="<?= isset($_POST['name']) ? htmlspecialchars($_POST['name']) : '' ?>" required>
                </div>

                <div class="form-group">
                  <label for="password"><i class="bi bi-shield-lock me-2"></i>Room Password</label>
                  <input type="password" class="form-control" id="password" name="password"
                         placeholder="Enter room password" required>
                </div>

                <button type="submit" class="btn-join">
                  <i class="bi bi-box-arrow-in-right me-2"></i>Join Room
                </button>
              </form>

              <div class="help-section">
                <h6><i class="bi bi-question-circle me-2"></i>Don't have a room code?</h6>
                <p>Create your own chat room and invite friends to join</p>
                <a href="create_room.php" class="btn-create-alt">
                  <i class="bi bi-plus-circle me-2"></i>Create New Room
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer id="footer" class="footer dark-background">
    <div class="container">
      <div class="row gy-3">
        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-geo-alt icon"></i>
          <div class="address">
            <h4>Address</h4>
            <p>QuickMeet HQ<br>Digital Communication Center<br></p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-telephone icon"></i>
          <div>
            <h4>Contact</h4>
            <p>
              <strong>Phone:</strong> <span>+91 7028844513</span><br>
              <strong>Email:</strong> <span><EMAIL></span><br>
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-clock icon"></i>
          <div>
            <h4>Available</h4>
            <p>
              <strong>24/7 Service</strong><br>
              Always online and ready to connect you with friends and family.
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6">
          <h4>Follow Us</h4>
          <div class="social-links d-flex">
            <a href="#" class="twitter"><i class="bi bi-twitter-x"></i></a>
            <a href="#" class="facebook"><i class="bi bi-facebook"></i></a>
            <a href="#" class="instagram"><i class="bi bi-instagram"></i></a>
            <a href="#" class="linkedin"><i class="bi bi-linkedin"></i></a>
          </div>
        </div>
      </div>
    </div>

    <div class="container copyright text-center mt-4">
      <p>© <span>Copyright</span> <strong class="px-1 sitename">QuickMeet</strong> <span>All Rights Reserved</span></p>
    </div>
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <script>
    // Auto-format room code input
    document.getElementById('room_code').addEventListener('input', function(e) {
      let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
      if (value.length > 6) value = value.slice(0, 6); // Limit to 6 digits
      e.target.value = value;
    });

    // Form validation
    document.getElementById('joinRoomForm').addEventListener('submit', function(e) {
      const roomCode = document.getElementById('room_code').value;
      if (roomCode.length !== 6) {
        e.preventDefault();
        alert('Room code must be exactly 6 digits');
        return false;
      }
    });

    // Paste room code function
    async function pasteRoomCode() {
      try {
        const text = await navigator.clipboard.readText();
        const roomCodeInput = document.getElementById('room_code');

        // Extract 6-digit numbers from pasted text
        const sixDigitMatch = text.match(/\b\d{6}\b/);

        if (sixDigitMatch) {
          roomCodeInput.value = sixDigitMatch[0];
          roomCodeInput.focus();

          // Show success feedback
          showPasteSuccess();
        } else {
          // If no 6-digit number found, just paste the text and let user edit
          const cleanText = text.replace(/\D/g, '').slice(0, 6);
          if (cleanText) {
            roomCodeInput.value = cleanText;
            roomCodeInput.focus();
            showPasteSuccess();
          } else {
            showPasteError('No valid room code found in clipboard');
          }
        }
      } catch (err) {
        // Fallback for browsers that don't support clipboard API
        showPasteError('Unable to access clipboard. Please paste manually.');
        document.getElementById('room_code').focus();
      }
    }

    // Show paste success feedback
    function showPasteSuccess() {
      const btn = document.querySelector('.btn-paste');
      const originalHTML = btn.innerHTML;
      btn.innerHTML = '<i class="bi bi-check"></i>';
      btn.style.background = '#28a745';

      setTimeout(() => {
        btn.innerHTML = originalHTML;
        btn.style.background = 'var(--accent-color)';
      }, 1000);
    }

    // Show paste error feedback
    function showPasteError(message) {
      const btn = document.querySelector('.btn-paste');
      const originalHTML = btn.innerHTML;
      btn.innerHTML = '<i class="bi bi-x"></i>';
      btn.style.background = '#dc3545';

      setTimeout(() => {
        btn.innerHTML = originalHTML;
        btn.style.background = 'var(--accent-color)';
      }, 1000);

      // Show error message
      alert(message);
    }

    // Initialize AOS
    AOS.init();
  </script>

</body>
</html>