<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickMeet Database Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .step {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 QuickMeet Database Setup</h1>
        
        <?php
        if (isset($_POST['setup_database'])) {
            echo "<h2>Setting up QuickMeet Database...</h2>";
            
            // Database connection parameters
            $servername = "localhost";
            $username = "root";
            $password = "12345678";  // Your MySQL password
            
            try {
                // Connect to MySQL server (without database)
                $conn = new mysqli($servername, $username, $password);
                
                if ($conn->connect_error) {
                    throw new Exception("Connection failed: " . $conn->connect_error);
                }
                
                echo "<div class='step success'>✅ Connected to MySQL server</div>";
                
                // Set charset
                $conn->set_charset("utf8mb4");
                
                // Read and execute SQL file
                $sql_file = 'quickmeet_complete_database.sql';
                if (!file_exists($sql_file)) {
                    throw new Exception("SQL file not found: $sql_file");
                }
                
                $sql_content = file_get_contents($sql_file);
                
                // Split SQL into individual statements
                $statements = array_filter(array_map('trim', explode(';', $sql_content)));
                
                $success_count = 0;
                $error_count = 0;
                
                foreach ($statements as $statement) {
                    if (empty($statement) || strpos($statement, '--') === 0) continue;
                    
                    try {
                        if ($conn->query($statement)) {
                            $success_count++;
                            
                            // Show important operations
                            if (stripos($statement, 'CREATE DATABASE') !== false) {
                                echo "<div class='step success'>✅ Created database: quickmeet_chat</div>";
                            } elseif (stripos($statement, 'CREATE TABLE') !== false) {
                                preg_match('/CREATE TABLE.*?`(\w+)`/i', $statement, $matches);
                                if (isset($matches[1])) {
                                    echo "<div class='step success'>✅ Created table: {$matches[1]}</div>";
                                }
                            }
                        } else {
                            $error_count++;
                            echo "<div class='step error'>❌ Error: " . $conn->error . "</div>";
                        }
                    } catch (Exception $e) {
                        $error_count++;
                        echo "<div class='step warning'>⚠️ Warning: " . $e->getMessage() . "</div>";
                    }
                }
                
                echo "<div class='step info'>📊 Executed $success_count statements successfully</div>";
                if ($error_count > 0) {
                    echo "<div class='step warning'>⚠️ $error_count statements had warnings (this is normal for existing structures)</div>";
                }
                
                // Create upload directories
                echo "<h3>Creating Upload Directories:</h3>";
                $directories = ['uploads', 'uploads/images', 'uploads/documents', 'uploads/other'];
                
                foreach ($directories as $dir) {
                    if (!is_dir($dir)) {
                        if (mkdir($dir, 0755, true)) {
                            echo "<div class='step success'>✅ Created directory: $dir</div>";
                        } else {
                            echo "<div class='step error'>❌ Failed to create directory: $dir</div>";
                        }
                    } else {
                        echo "<div class='step info'>ℹ️ Directory already exists: $dir</div>";
                    }
                }
                
                // Create .htaccess for security
                $htaccess_content = "# Prevent direct access to uploaded files
Options -Indexes

# Allow only specific file types
<FilesMatch \"\\.(jpg|jpeg|png|gif|webp|bmp|pdf|doc|docx|txt|rtf|xls|xlsx|ppt|pptx|zip|rar|7z|mp3|mp4|avi|mov)$\">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Deny access to PHP files in uploads
<FilesMatch \"\\.php$\">
    Order Deny,Allow
    Deny from all
</FilesMatch>";
                
                if (file_put_contents('uploads/.htaccess', $htaccess_content)) {
                    echo "<div class='step success'>✅ Created security .htaccess file</div>";
                } else {
                    echo "<div class='step warning'>⚠️ Could not create .htaccess file</div>";
                }
                
                echo "<h2 class='success'>🎉 Database Setup Complete!</h2>";
                echo "<div class='step success'>";
                echo "<h3>✅ Your QuickMeet application is ready!</h3>";
                echo "<p><strong>Database:</strong> quickmeet_chat</p>";
                echo "<p><strong>Tables created:</strong> rooms, messages, chat_files, admin_logs</p>";
                echo "<p><strong>Sample data:</strong> Added for testing</p>";
                echo "<p><strong>File uploads:</strong> Configured and secured</p>";
                echo "</div>";
                
                echo "<a href='index.php' class='btn btn-success'>🏠 Go to QuickMeet Homepage</a>";
                echo "<a href='admin.php' class='btn'>🛡️ Admin Dashboard</a>";
                
                $conn->close();
                
            } catch (Exception $e) {
                echo "<div class='step error'>❌ Setup failed: " . $e->getMessage() . "</div>";
                echo "<p>Please check your database credentials and try again.</p>";
            }
            
        } else {
            ?>
            <p>This script will set up the complete QuickMeet database with all required tables and features.</p>
            
            <div class="step">
                <h3>📋 What this setup includes:</h3>
                <ul>
                    <li>✅ Create <strong>quickmeet_chat</strong> database</li>
                    <li>✅ Create all required tables (rooms, messages, chat_files, admin_logs)</li>
                    <li>✅ Add sample data for testing</li>
                    <li>✅ Set up file upload directories</li>
                    <li>✅ Configure security settings</li>
                    <li>✅ Create database views and procedures</li>
                </ul>
            </div>
            
            <div class="step">
                <h3>⚙️ Database Configuration:</h3>
                <ul>
                    <li><strong>Server:</strong> localhost</li>
                    <li><strong>Username:</strong> root</li>
                    <li><strong>Password:</strong> 12345678</li>
                    <li><strong>Database:</strong> quickmeet_chat</li>
                </ul>
                <p><small>Make sure these credentials match your MySQL setup.</small></p>
            </div>
            
            <form method="post">
                <button type="submit" name="setup_database" class="btn btn-success">
                    🚀 Setup QuickMeet Database
                </button>
            </form>
            
            <p><small><strong>Note:</strong> This will create a new database. Any existing 'quickmeet_chat' database will be updated.</small></p>
            <?php
        }
        ?>
    </div>
</body>
</html>
