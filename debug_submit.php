<?php
// Debug script for number submission
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "DEBUG: Starting number submission debug\n";

// Check if POST data exists
echo "POST data: " . print_r($_POST, true) . "\n";

$room_code = $_POST['room_code'] ?? '';
$player_name = $_POST['player_name'] ?? '';
$chosen_number = intval($_POST['chosen_number'] ?? -1);

echo "Parsed data: Room=$room_code, Player=$player_name, Number=$chosen_number\n";

// Test database connection
include 'db.php';

if (!$conn) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

echo "Database connected successfully\n";

// Check if room exists
$room_check = $conn->prepare("SELECT room_code, status, current_round FROM game_rooms WHERE room_code = ?");
$room_check->bind_param("s", $room_code);
$room_check->execute();
$room_result = $room_check->get_result();

if ($room_result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Room not found: ' . $room_code]);
    exit;
}

$room_data = $room_result->fetch_assoc();
echo "Room data: " . print_r($room_data, true) . "\n";

// Check if player exists
$player_check = $conn->prepare("SELECT player_name, status FROM game_players WHERE room_code = ? AND player_name = ?");
$player_check->bind_param("ss", $room_code, $player_name);
$player_check->execute();
$player_result = $player_check->get_result();

if ($player_result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Player not found: ' . $player_name]);
    exit;
}

$player_data = $player_result->fetch_assoc();
echo "Player data: " . print_r($player_data, true) . "\n";

// Check if game is playing
if ($room_data['status'] !== 'playing') {
    echo json_encode(['success' => false, 'message' => 'Game not active. Current status: ' . $room_data['status']]);
    exit;
}

// Try to submit number
$current_round = $room_data['current_round'];
echo "Submitting for round: $current_round\n";

$stmt = $conn->prepare("INSERT INTO game_rounds (room_code, round_number, player_name, chosen_number, is_submitted) VALUES (?, ?, ?, ?, TRUE) ON DUPLICATE KEY UPDATE chosen_number = ?, is_submitted = TRUE");
$stmt->bind_param("sisii", $room_code, $current_round, $player_name, $chosen_number, $chosen_number);

if ($stmt->execute()) {
    echo "Number submitted successfully!\n";
    echo json_encode(['success' => true, 'message' => 'Number submitted successfully']);
} else {
    echo "SQL Error: " . $conn->error . "\n";
    echo json_encode(['success' => false, 'message' => 'SQL Error: ' . $conn->error]);
}

$conn->close();
?>
