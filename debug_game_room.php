<?php 
include 'db.php'; 

$room_code = $_GET['code'] ?? 'TEST01';
$player_name = $_GET['name'] ?? 'TestPlayer';

echo "<h1>Debug Game Room</h1>";
echo "<p>Room Code: $room_code</p>";
echo "<p>Player Name: $player_name</p>";

// Check database connection
if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ Database connected</p>";
}

// Check if game tables exist
$tables = ['game_rooms', 'game_players', 'game_rounds', 'game_results'];
foreach ($tables as $table) {
    $check = $conn->query("SHOW TABLES LIKE '$table'");
    if ($check && $check->num_rows > 0) {
        echo "<p style='color: green;'>✅ Table '$table' exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Table '$table' missing</p>";
    }
}

// Check if room exists
$room_check = $conn->prepare("SELECT * FROM game_rooms WHERE room_code = ?");
$room_check->bind_param("s", $room_code);
$room_check->execute();
$room_result = $room_check->get_result();

if ($room_result->num_rows > 0) {
    $room = $room_result->fetch_assoc();
    echo "<p style='color: green;'>✅ Room exists</p>";
    echo "<pre>" . print_r($room, true) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ Room does not exist</p>";
    echo "<p>Creating test room...</p>";
    
    // Create test room
    $create_room = $conn->prepare("INSERT INTO game_rooms (room_code, room_name, host_name, status) VALUES (?, 'Test Room', ?, 'waiting')");
    $create_room->bind_param("ss", $room_code, $player_name);
    
    if ($create_room->execute()) {
        echo "<p style='color: green;'>✅ Test room created</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create room: " . $conn->error . "</p>";
    }
}

// Check if player exists
$player_check = $conn->prepare("SELECT * FROM game_players WHERE room_code = ? AND player_name = ?");
$player_check->bind_param("ss", $room_code, $player_name);
$player_check->execute();
$player_result = $player_check->get_result();

if ($player_result->num_rows > 0) {
    echo "<p style='color: green;'>✅ Player exists in room</p>";
} else {
    echo "<p style='color: red;'>❌ Player not in room</p>";
    echo "<p>Adding player to room...</p>";
    
    // Add player to room
    $add_player = $conn->prepare("INSERT INTO game_players (room_code, player_name, player_id, score, status) VALUES (?, ?, ?, 0, 'active')");
    $player_id = uniqid();
    $add_player->bind_param("sss", $room_code, $player_name, $player_id);
    
    if ($add_player->execute()) {
        echo "<p style='color: green;'>✅ Player added to room</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to add player: " . $conn->error . "</p>";
    }
}

// Test game handler
echo "<h2>Testing Game Handler</h2>";
$test_url = "game_handler.php?action=get_game_state&room_code=" . urlencode($room_code);
echo "<p>Testing URL: <a href='$test_url' target='_blank'>$test_url</a></p>";

// Test the game handler directly
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://localhost:3000/" . $test_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p>HTTP Code: $http_code</p>";
echo "<p>Response:</p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

// Show all players in room
$all_players = $conn->prepare("SELECT * FROM game_players WHERE room_code = ?");
$all_players->bind_param("s", $room_code);
$all_players->execute();
$players_result = $all_players->get_result();

echo "<h2>All Players in Room</h2>";
while ($player = $players_result->fetch_assoc()) {
    echo "<pre>" . print_r($player, true) . "</pre>";
}

echo "<hr>";
echo "<p><a href='game_room.php?code=$room_code&name=$player_name'>Go to Game Room</a></p>";
echo "<p><a href='index.php'>Back to Home</a></p>";
?>
