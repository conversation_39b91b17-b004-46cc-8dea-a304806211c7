-- MySQL Workbench Script for Chat App Database
-- Run this script in MySQL Workbench to create the database and tables

-- Create the database
CREATE DATABASE IF NOT EXISTS `chat_app` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE `chat_app`;

-- Create rooms table
CREATE TABLE IF NOT EXISTS `rooms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_code` varchar(10) NOT NULL UNIQUE,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `age` int(3) NOT NULL,
  `gender` enum('Male','Female','Other') NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `room_code_UNIQUE` (`room_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create messages table
CREATE TABLE IF NOT EXISTS `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_code` varchar(10) NOT NULL,
  `sender_name` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `message_type` enum('text','image','file') DEFAULT 'text',
  PRIMARY KEY (`id`),
  KEY `fk_messages_room_code` (`room_code`),
  CONSTRAINT `fk_messages_room_code` FOREIGN KEY (`room_code`) REFERENCES `rooms` (`room_code`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create participants table (for tracking who joined which room)
CREATE TABLE IF NOT EXISTS `participants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_code` varchar(10) NOT NULL,
  `participant_name` varchar(100) NOT NULL,
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_online` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `fk_participants_room_code` (`room_code`),
  CONSTRAINT `fk_participants_room_code` FOREIGN KEY (`room_code`) REFERENCES `rooms` (`room_code`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some sample data (optional)
-- Uncomment the lines below if you want sample data

-- INSERT INTO `rooms` (`room_code`, `name`, `phone`, `age`, `gender`, `password`) VALUES
-- ('123456', 'John Doe', '1234567890', 25, 'Male', 'password123'),
-- ('789012', 'Jane Smith', '0987654321', 22, 'Female', 'mypassword');

-- INSERT INTO `messages` (`room_code`, `sender_name`, `message`) VALUES
-- ('123456', 'John Doe', 'Hello! Welcome to my chat room.'),
-- ('123456', 'Alice', 'Hi John! Thanks for creating this room.'),
-- ('789012', 'Jane Smith', 'This is my private chat room.');

-- Show tables to verify creation
SHOW TABLES;

-- Show table structures
DESCRIBE rooms;
DESCRIBE messages;
DESCRIBE participants;

-- Display success message
SELECT 'Database and tables created successfully!' AS Status;
