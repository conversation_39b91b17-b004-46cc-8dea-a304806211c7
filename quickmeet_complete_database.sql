-- QuickMeet Complete Database Setup
-- This file creates the complete database structure for QuickMeet chat application
-- Run this file to set up everything from scratch

-- Create database
CREATE DATABASE IF NOT EXISTS quickmeet_chat;
USE quickmeet_chat;

-- Set charset and collation
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- Table: rooms
-- Stores chat room information and host details
-- =====================================================
CREATE TABLE IF NOT EXISTS `rooms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_code` varchar(10) NOT NULL UNIQUE,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `age` int(3) NOT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_room_code` (`room_code`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_gender` (`gender`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Table: messages
-- Stores all chat messages and file attachments
-- =====================================================
CREATE TABLE IF NOT EXISTS `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_code` varchar(10) NOT NULL,
  `sender_name` varchar(100) NOT NULL,
  `message` text NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `file_type` varchar(50) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_room_code` (`room_code`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_room_timestamp` (`room_code`, `timestamp`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_sender` (`sender_name`),
  CONSTRAINT `fk_messages_room` FOREIGN KEY (`room_code`) REFERENCES `rooms` (`room_code`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Table: chat_files (Optional - for better file management)
-- Alternative approach for file management
-- =====================================================
CREATE TABLE IF NOT EXISTS `chat_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_type` varchar(50) NOT NULL,
  `file_size` int(11) NOT NULL,
  `uploaded_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_uploaded_at` (`uploaded_at`),
  CONSTRAINT `fk_files_message` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Table: admin_logs (Optional - for admin activity tracking)
-- Track admin dashboard activities
-- =====================================================
CREATE TABLE IF NOT EXISTS `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action` varchar(100) NOT NULL,
  `details` text,
  `ip_address` varchar(45),
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Insert Sample Data (Optional - for testing)
-- =====================================================

-- Sample rooms
INSERT INTO `rooms` (`room_code`, `name`, `phone`, `age`, `gender`, `password`) VALUES
('123456', 'John Doe', '+91 9876543210', 25, 'male', 'password123'),
('789012', 'Jane Smith', '+91 8765432109', 23, 'female', 'mypass456'),
('345678', 'Alex Johnson', '+91 7654321098', 28, 'other', 'secure789');

-- Sample messages
INSERT INTO `messages` (`room_code`, `sender_name`, `message`) VALUES
('123456', 'John Doe', 'Welcome to my chat room!'),
('123456', 'Alice', 'Hi John! Thanks for inviting me.'),
('123456', 'Bob', 'Hello everyone! 👋'),
('789012', 'Jane Smith', 'This is my room for project discussion.'),
('789012', 'Mike', 'Great! Let\'s start planning.'),
('345678', 'Alex Johnson', 'Welcome to the study group!'),
('345678', 'Sarah', 'Ready to learn together! 📚');

-- =====================================================
-- Create Views for Better Data Access
-- =====================================================

-- View: Room Statistics
CREATE OR REPLACE VIEW `room_stats` AS
SELECT 
    r.room_code,
    r.name as host_name,
    r.phone,
    r.age,
    r.gender,
    r.created_at,
    COUNT(m.id) as message_count,
    COUNT(DISTINCT m.sender_name) as participant_count,
    MAX(m.timestamp) as last_activity,
    COUNT(CASE WHEN m.file_path IS NOT NULL THEN 1 END) as files_shared
FROM rooms r
LEFT JOIN messages m ON r.room_code = m.room_code
GROUP BY r.id, r.room_code, r.name, r.phone, r.age, r.gender, r.created_at;

-- View: Recent Activity
CREATE OR REPLACE VIEW `recent_activity` AS
SELECT 
    m.id,
    m.room_code,
    r.name as room_host,
    m.sender_name,
    m.message,
    m.file_name,
    m.timestamp,
    CASE 
        WHEN m.file_path IS NOT NULL THEN 'file'
        ELSE 'message'
    END as message_type
FROM messages m
JOIN rooms r ON m.room_code = r.room_code
ORDER BY m.timestamp DESC
LIMIT 100;

-- =====================================================
-- Create Stored Procedures for Common Operations
-- =====================================================

DELIMITER //

-- Procedure: Clean old rooms (older than 30 days with no activity)
CREATE PROCEDURE CleanOldRooms()
BEGIN
    DELETE r FROM rooms r
    LEFT JOIN messages m ON r.room_code = m.room_code
    WHERE r.created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    AND (m.timestamp IS NULL OR m.timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY));
END //

-- Procedure: Get room statistics
CREATE PROCEDURE GetRoomStats(IN room_code_param VARCHAR(10))
BEGIN
    SELECT 
        r.*,
        COUNT(m.id) as total_messages,
        COUNT(DISTINCT m.sender_name) as unique_participants,
        COUNT(CASE WHEN m.file_path IS NOT NULL THEN 1 END) as files_shared,
        MAX(m.timestamp) as last_activity
    FROM rooms r
    LEFT JOIN messages m ON r.room_code = m.room_code
    WHERE r.room_code = room_code_param
    GROUP BY r.id;
END //

-- Procedure: Delete room and all associated data
CREATE PROCEDURE DeleteRoom(IN room_code_param VARCHAR(10))
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Delete associated files from chat_files table
    DELETE cf FROM chat_files cf
    JOIN messages m ON cf.message_id = m.id
    WHERE m.room_code = room_code_param;
    
    -- Delete messages (this will cascade to chat_files due to FK)
    DELETE FROM messages WHERE room_code = room_code_param;
    
    -- Delete room
    DELETE FROM rooms WHERE room_code = room_code_param;
    
    COMMIT;
END //

DELIMITER ;

-- =====================================================
-- Create Triggers for Data Integrity
-- =====================================================

DELIMITER //

-- Trigger: Update room timestamp when new message is added
CREATE TRIGGER update_room_activity 
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    UPDATE rooms 
    SET updated_at = NOW() 
    WHERE room_code = NEW.room_code;
END //

-- Trigger: Log admin actions (if admin_logs table is used)
CREATE TRIGGER log_room_deletion
AFTER DELETE ON rooms
FOR EACH ROW
BEGIN
    INSERT INTO admin_logs (action, details, created_at)
    VALUES ('ROOM_DELETED', CONCAT('Room ', OLD.room_code, ' deleted'), NOW());
END //

DELIMITER ;

-- =====================================================
-- Set Permissions and Final Setup
-- =====================================================

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Create indexes for better performance
CREATE INDEX idx_messages_room_sender ON messages(room_code, sender_name);
CREATE INDEX idx_messages_timestamp_desc ON messages(timestamp DESC);

-- =====================================================
-- Database Setup Complete
-- =====================================================

SELECT 'QuickMeet Database Setup Complete!' as Status;
SELECT COUNT(*) as Total_Tables FROM information_schema.tables WHERE table_schema = 'quickmeet_chat';
SELECT 'Remember to create uploads/ directory structure' as Note;
