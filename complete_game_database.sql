-- ========================================
-- QuickMeet Mathematical Strategy Game
-- Complete Database Setup - Start to End
-- ========================================
-- This file contains ALL database queries needed for the game
-- Run this if you need to recreate the entire database from scratch

-- ========================================
-- 1. DATABASE CREATION (Optional)
-- ========================================
-- Uncomment these lines if you need to create a new database
-- CREATE DATABASE IF NOT EXISTS quickmeet_game;
-- USE quickmeet_game;

-- ========================================
-- 2. DROP EXISTING TABLES (Clean Start)
-- ========================================
-- Drop tables in correct order to avoid foreign key constraints
DROP TABLE IF EXISTS game_results;
DROP TABLE IF EXISTS game_rounds;
DROP TABLE IF EXISTS game_players;
DROP TABLE IF EXISTS game_rooms;

-- ========================================
-- 3. CREATE GAME TABLES
-- ========================================

-- Game rooms table (main game sessions)
CREATE TABLE game_rooms (
    room_code VARCHAR(10) PRIMARY KEY,
    room_name VARCHAR(100) NOT NULL,
    host_name VARCHAR(50) NOT NULL,
    status ENUM('waiting', 'playing', 'break', 'finished') DEFAULT 'waiting',
    current_round INT DEFAULT 0,
    round_start_time TIMESTAMP NULL,
    max_players INT DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Game players table (player data and scores)
CREATE TABLE game_players (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_code VARCHAR(10) NOT NULL,
    player_name VARCHAR(50) NOT NULL,
    player_id VARCHAR(20) NOT NULL,
    score INT DEFAULT 0,
    status ENUM('active', 'eliminated', 'disconnected') DEFAULT 'active',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_code) REFERENCES game_rooms(room_code) ON DELETE CASCADE,
    UNIQUE KEY unique_player_room (room_code, player_name)
);

-- Game rounds table (number submissions per round)
CREATE TABLE game_rounds (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_code VARCHAR(10) NOT NULL,
    round_number INT NOT NULL,
    player_name VARCHAR(50) NOT NULL,
    chosen_number INT NOT NULL,
    is_submitted BOOLEAN DEFAULT FALSE,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_code) REFERENCES game_rooms(room_code) ON DELETE CASCADE,
    UNIQUE KEY unique_round_player (room_code, round_number, player_name)
);

-- Game results table (round calculations and progressive rules)
CREATE TABLE game_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_code VARCHAR(10) NOT NULL,
    round_number INT NOT NULL,
    average_number DECIMAL(5,2) NOT NULL,
    target_number DECIMAL(5,2) NOT NULL,
    winner_name VARCHAR(50) NOT NULL,
    rules_applied VARCHAR(255) DEFAULT '',
    rule_messages TEXT,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_code) REFERENCES game_rooms(room_code) ON DELETE CASCADE
);

-- ========================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- ========================================

-- Game rooms indexes
CREATE INDEX idx_game_rooms_status ON game_rooms(status);
CREATE INDEX idx_game_rooms_created ON game_rooms(created_at);

-- Game players indexes
CREATE INDEX idx_game_players_room ON game_players(room_code);
CREATE INDEX idx_game_players_status ON game_players(status);
CREATE INDEX idx_game_players_score ON game_players(score);

-- Game rounds indexes
CREATE INDEX idx_game_rounds_room_round ON game_rounds(room_code, round_number);
CREATE INDEX idx_game_rounds_submitted ON game_rounds(is_submitted);

-- Game results indexes
CREATE INDEX idx_game_results_room ON game_results(room_code);
CREATE INDEX idx_game_results_round ON game_results(round_number);

-- ========================================
-- 5. INSERT SAMPLE DATA (Optional)
-- ========================================

-- Insert a test game room for verification
INSERT INTO game_rooms (room_code, room_name, host_name, status) 
VALUES ('TEST01', 'Test Game Room', 'Admin', 'waiting');

-- Insert test players
INSERT INTO game_players (room_code, player_name, player_id, score, status) 
VALUES 
('TEST01', 'Admin', 'admin_001', 0, 'active'),
('TEST01', 'Player2', 'player_002', 0, 'active');

-- ========================================
-- 6. VERIFY DATABASE SETUP
-- ========================================

-- Show all created tables
SHOW TABLES;

-- Verify table structures
DESCRIBE game_rooms;
DESCRIBE game_players;
DESCRIBE game_rounds;
DESCRIBE game_results;

-- Count records in each table
SELECT 'game_rooms' as table_name, COUNT(*) as record_count FROM game_rooms
UNION ALL
SELECT 'game_players' as table_name, COUNT(*) as record_count FROM game_players
UNION ALL
SELECT 'game_rounds' as table_name, COUNT(*) as record_count FROM game_rounds
UNION ALL
SELECT 'game_results' as table_name, COUNT(*) as record_count FROM game_results;

-- ========================================
-- 7. CLEANUP QUERIES (Use when needed)
-- ========================================

-- Delete all game data (keep tables)
-- DELETE FROM game_results;
-- DELETE FROM game_rounds;
-- DELETE FROM game_players;
-- DELETE FROM game_rooms;

-- Reset auto increment counters
-- ALTER TABLE game_players AUTO_INCREMENT = 1;
-- ALTER TABLE game_rounds AUTO_INCREMENT = 1;
-- ALTER TABLE game_results AUTO_INCREMENT = 1;

-- ========================================
-- 8. PROGRESSIVE RULES REFERENCE
-- ========================================
/*
PROGRESSIVE ELIMINATION RULES:

Rule 1 (4+ players): Duplicate Disqualification
- If 2+ players choose same number → Disqualified (-1 point each)

Rule 2 (3+ players): Double Penalty  
- Round losers get -2 points instead of -1

Rule 3 (2 players): Zero vs Hundred
- If one chooses 0 and other chooses 100 → Player with 100 wins

RULE ACTIVATION:
- 5 players: Standard rules only
- 4 players: Rule 1 activates
- 3 players: Rules 1+2 activate
- 2 players: Rules 1+2+3 activate

SCORING SYSTEM:
- Winner: +1 point
- Loser: -1 point (or -2 with Rule 2)
- Disqualified: -1 point (Rule 1)
- Elimination: At -10 points
*/

-- ========================================
-- SETUP COMPLETE!
-- ========================================
-- Your QuickMeet Mathematical Strategy Game database is ready!
-- 
-- Next steps:
-- 1. Configure db.php with your database credentials
-- 2. Visit check_game_db.php to verify setup
-- 3. Start playing at index.php
-- 
-- Game Features:
-- ✅ 2-5 player mathematical strategy game
-- ✅ Progressive elimination rules
-- ✅ Real-time scoring and leaderboards
-- ✅ 1-minute timed rounds
-- ✅ Automatic game progression
-- ========================================
