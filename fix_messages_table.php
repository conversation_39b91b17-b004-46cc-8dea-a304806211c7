<!DOCTYPE html>
<html>
<head>
    <title>Fix Messages Table - QuickMeet</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 10px 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 Fix Messages Table for Game Integration</h1>
    
    <?php
    include 'db.php';
    
    echo "<h2>📊 Checking Messages Table</h2>";
    
    if ($conn->connect_error) {
        echo "<div class='status error'>❌ Database connection failed: " . $conn->connect_error . "</div>";
        exit;
    }
    
    // Check if messages table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'messages'");
    if ($table_check->num_rows === 0) {
        echo "<div class='status error'>❌ Messages table doesn't exist. Please run the main database setup first.</div>";
        echo "<a href='setup_database.php' class='btn'>Setup Database</a>";
        exit;
    }
    
    echo "<div class='status success'>✅ Messages table exists</div>";
    
    // Check current table structure
    echo "<h3>📋 Current Table Structure</h3>";
    $structure = $conn->query("DESCRIBE messages");
    $columns = [];
    
    echo "<div style='background: white; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Existing Columns:</h4><ul>";
    
    while ($row = $structure->fetch_assoc()) {
        $columns[] = $row['Field'];
        echo "<li><strong>" . $row['Field'] . "</strong> - " . $row['Type'] . "</li>";
    }
    echo "</ul></div>";
    
    // Check for missing columns
    $missing_columns = [];
    if (!in_array('game_room', $columns)) {
        $missing_columns[] = 'game_room';
    }
    if (!in_array('message_type', $columns)) {
        $missing_columns[] = 'message_type';
    }
    
    if (empty($missing_columns)) {
        echo "<div class='status success'>";
        echo "<h3>🎉 Messages table is already properly configured!</h3>";
        echo "<p>All required columns exist. You can now use the game features.</p>";
        echo "</div>";
        
        echo "<div style='text-align: center; margin: 20px 0;'>";
        echo "<a href='create_game.php' class='btn'>🎮 Create Game</a>";
        echo "<a href='join_game.php' class='btn'>🎯 Join Game</a>";
        echo "<a href='index.php' class='btn'>🏠 Home</a>";
        echo "</div>";
    } else {
        echo "<div class='status error'>";
        echo "<h3>⚠️ Missing Columns Found</h3>";
        echo "<p>Missing columns: " . implode(', ', $missing_columns) . "</p>";
        echo "</div>";
        
        echo "<h3>🔧 Fixing Table Structure</h3>";
        
        $success_count = 0;
        $error_count = 0;
        
        // Add game_room column
        if (in_array('game_room', $missing_columns)) {
            echo "<p><strong>Adding game_room column...</strong></p>";
            if ($conn->query("ALTER TABLE messages ADD COLUMN game_room BOOLEAN DEFAULT FALSE")) {
                echo "<div class='status success'>✅ Added game_room column</div>";
                $success_count++;
            } else {
                echo "<div class='status error'>❌ Failed to add game_room column: " . $conn->error . "</div>";
                $error_count++;
            }
        }
        
        // Add message_type column
        if (in_array('message_type', $missing_columns)) {
            echo "<p><strong>Adding message_type column...</strong></p>";
            if ($conn->query("ALTER TABLE messages ADD COLUMN message_type ENUM('chat', 'system', 'game') DEFAULT 'chat'")) {
                echo "<div class='status success'>✅ Added message_type column</div>";
                $success_count++;
            } else {
                echo "<div class='status error'>❌ Failed to add message_type column: " . $conn->error . "</div>";
                $error_count++;
            }
        }
        
        // Add index
        echo "<p><strong>Adding performance index...</strong></p>";
        if ($conn->query("CREATE INDEX IF NOT EXISTS idx_messages_game_room ON messages(room_code, game_room)")) {
            echo "<div class='status success'>✅ Added performance index</div>";
            $success_count++;
        } else {
            echo "<div class='status error'>❌ Failed to add index: " . $conn->error . "</div>";
            $error_count++;
        }
        
        echo "<h3>📈 Fix Summary</h3>";
        echo "<p><strong>Successful operations:</strong> $success_count</p>";
        echo "<p><strong>Errors:</strong> $error_count</p>";
        
        if ($error_count === 0) {
            echo "<div class='status success'>";
            echo "<h3>🎉 Messages table fixed successfully!</h3>";
            echo "<p>All required columns have been added. You can now use the game features.</p>";
            echo "</div>";
            
            echo "<div style='text-align: center; margin: 20px 0;'>";
            echo "<a href='create_game.php' class='btn'>🎮 Create Game</a>";
            echo "<a href='join_game.php' class='btn'>🎯 Join Game</a>";
            echo "<a href='index.php' class='btn'>🏠 Home</a>";
            echo "</div>";
        } else {
            echo "<div class='status error'>";
            echo "<h3>⚠️ Some errors occurred</h3>";
            echo "<p>Please check the errors above and try running the SQL manually.</p>";
            echo "</div>";
        }
    }
    
    // Show updated table structure
    echo "<h3>📋 Updated Table Structure</h3>";
    $updated_structure = $conn->query("DESCRIBE messages");
    
    echo "<div style='background: white; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>All Columns:</h4><ul>";
    
    while ($row = $updated_structure->fetch_assoc()) {
        $is_new = in_array($row['Field'], ['game_room', 'message_type']);
        $highlight = $is_new ? ' style="color: #28a745; font-weight: bold;"' : '';
        echo "<li$highlight><strong>" . $row['Field'] . "</strong> - " . $row['Type'];
        if ($is_new) echo " <span style='background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;'>NEW</span>";
        echo "</li>";
    }
    echo "</ul></div>";
    
    $conn->close();
    ?>
    
    <hr>
    <p style="text-align: center; color: #666;">
        <small>QuickMeet Messages Table Fix Tool</small>
    </p>
</body>
</html>
