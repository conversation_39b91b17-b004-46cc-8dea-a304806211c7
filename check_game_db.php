<!DOCTYPE html>
<html>
<head>
    <title>QuickMeet Game Database Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
    </style>
</head>
<body>
    <h1>🎮 QuickMeet Game Database Check</h1>
    
    <?php
    include 'db.php';
    
    $required_tables = [
        'game_rooms' => 'Game rooms storage',
        'game_players' => 'Game players data',
        'game_rounds' => 'Game round submissions',
        'game_results' => 'Game round results'
    ];
    
    $missing_tables = [];
    $existing_tables = [];
    
    echo "<h2>📊 Database Connection</h2>";
    if ($conn->connect_error) {
        echo "<div class='status error'>❌ Database connection failed: " . $conn->connect_error . "</div>";
        exit;
    } else {
        echo "<div class='status success'>✅ Database connection successful</div>";
    }
    
    echo "<h2>📋 Required Tables Check</h2>";
    
    foreach ($required_tables as $table => $description) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            $existing_tables[] = $table;
            echo "<div class='status success'>✅ Table '$table' exists - $description</div>";
            
            // Check table structure
            $structure = $conn->query("DESCRIBE $table");
            if ($structure) {
                $columns = $structure->num_rows;
                echo "<div style='margin-left: 20px; color: #666;'>📝 $columns columns found</div>";
            }
        } else {
            $missing_tables[] = $table;
            echo "<div class='status error'>❌ Table '$table' missing - $description</div>";
        }
    }
    
    echo "<h2>📈 Summary</h2>";
    
    if (empty($missing_tables)) {
        echo "<div class='status success'>";
        echo "<h3>🎉 All game tables are set up correctly!</h3>";
        echo "<p>Your game database is ready. You can now:</p>";
        echo "<ul>";
        echo "<li>Create game rooms</li>";
        echo "<li>Join existing games</li>";
        echo "<li>Play the mathematical strategy game</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='text-align: center; margin: 20px 0;'>";
        echo "<a href='create_game.php' class='btn btn-success'>🎮 Create Game Room</a>";
        echo "<a href='join_game.php' class='btn btn-success'>🎯 Join Game Room</a>";
        echo "<a href='index.php' class='btn'>🏠 Back to Home</a>";
        echo "</div>";
        
    } else {
        echo "<div class='status error'>";
        echo "<h3>⚠️ Game database setup incomplete</h3>";
        echo "<p>Missing tables: " . implode(', ', $missing_tables) . "</p>";
        echo "<p>You need to run the game database setup to create these tables.</p>";
        echo "</div>";
        
        echo "<div style='text-align: center; margin: 20px 0;'>";
        echo "<a href='setup_game_db.php' class='btn btn-warning'>🛠️ Setup Game Database</a>";
        echo "<a href='index.php' class='btn'>🏠 Back to Home</a>";
        echo "</div>";
    }
    
    // Show existing regular tables
    echo "<h2>📊 All Database Tables</h2>";
    $all_tables = $conn->query("SHOW TABLES");
    if ($all_tables && $all_tables->num_rows > 0) {
        echo "<div style='background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;'>";
        echo "<h4>Existing Tables:</h4>";
        echo "<ul>";
        while ($row = $all_tables->fetch_array()) {
            $table_name = $row[0];
            $is_game_table = strpos($table_name, 'game_') === 0;
            $icon = $is_game_table ? '🎮' : '💬';
            echo "<li>$icon $table_name</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Test game handler
    echo "<h2>🔧 Game Handler Test</h2>";
    if (file_exists('game_handler.php')) {
        echo "<div class='status success'>✅ game_handler.php exists</div>";
        
        // Test if we can call it
        $test_url = 'game_handler.php?action=test';
        echo "<div style='margin: 10px 0;'>";
        echo "<a href='$test_url' target='_blank' class='btn'>🧪 Test Game Handler</a>";
        echo "</div>";
    } else {
        echo "<div class='status error'>❌ game_handler.php missing</div>";
    }
    
    $conn->close();
    ?>
    
    <hr>
    <p style="text-align: center; color: #666;">
        <small>QuickMeet Game System Diagnostic Tool</small>
    </p>
</body>
</html>
