<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Create Game Room - QuickMeet</title>
  <meta name="description" content="Create a mathematical strategy game room in QuickMeet">
  <meta name="keywords" content="QuickMeet, math game, strategy game, multiplayer">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    .game-section {
      min-height: 100vh;
      padding: 120px 0 80px 0;
      position: relative;
    }

    .game-form {
      background: var(--surface-color);
      border-radius: 20px;
      padding: 3rem;
      border: 1px solid rgba(206, 175, 127, 0.1);
      max-width: 600px;
      margin: 0 auto;
    }

    .form-title {
      color: var(--heading-color);
      font-size: 2.5rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 2rem;
      font-family: var(--heading-font);
    }

    .game-rules {
      background: rgba(206, 175, 127, 0.1);
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border-left: 4px solid var(--accent-color);
    }

    .rule-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
      color: var(--default-color);
    }

    .rule-item i {
      color: var(--accent-color);
      margin-right: 0.75rem;
      font-size: 1.2rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-label {
      color: var(--heading-color);
      font-weight: 600;
      margin-bottom: 0.5rem;
      display: block;
    }

    .form-control {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 12px;
      padding: 1rem 1.5rem;
      color: var(--default-color);
      font-size: 1rem;
      width: 100%;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--accent-color);
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 0 3px rgba(206, 175, 127, 0.2);
    }

    .form-control::placeholder {
      color: rgba(241, 243, 245, 0.6);
    }

    .btn-create {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 1rem 2rem;
      border-radius: 12px;
      font-weight: 600;
      font-size: 1.1rem;
      border: none;
      cursor: pointer;
      width: 100%;
      transition: all 0.3s ease;
    }

    .btn-create:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(206, 175, 127, 0.3);
    }

    .btn-back {
      background: transparent;
      border: 2px solid var(--accent-color);
      color: var(--accent-color);
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      text-decoration: none;
      font-weight: 600;
      display: inline-block;
      margin-bottom: 2rem;
      transition: all 0.3s ease;
    }

    .btn-back:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
    }
  </style>
</head>

<body class="index-page">

  <!-- Header -->
  <header id="header" class="header d-flex align-items-center sticky-top">
    <div class="container position-relative d-flex align-items-center justify-content-between">

      <a href="index.php" class="logo d-flex align-items-center me-auto me-xl-0">
        <img src="assets/img/bg/chat.png" alt="QuickMeet Logo" style="height: 40px; margin-right: 0.5rem;">
        <h1 class="sitename">QuickMeet</h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.php">Home</a></li>
          <li><a href="index.php#features">Features</a></li>
          <li><a href="index.php#how-it-works">How It Works</a></li>
          <li><a href="https://quickf.free.nf/" target="_blank">Quick</a></li>
          <li><a href="admin.php">Admin</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

      <div class="header-actions">
        <a class="cta-button" href="create_room.php">Create Chat Room</a>
      </div>

    </div>
  </header>

  <main class="main">

    <!-- Create Game Section -->
    <section class="game-section">
      <div class="container" data-aos="fade-up">
        
        <a href="index.php" class="btn-back">
          <i class="bi bi-arrow-left me-2"></i>Back to Home
        </a>

        <div class="game-form">
          <h1 class="form-title">🎮 Create Math Game</h1>
          
          <!-- Game Rules -->
          <div class="game-rules">
            <h4 style="color: var(--accent-color); margin-bottom: 1rem;">
              <i class="bi bi-info-circle me-2"></i>Game Rules
            </h4>
            <div class="rule-item">
              <i class="bi bi-people"></i>
              <span>2-5 Players compete in infinite rounds</span>
            </div>
            <div class="rule-item">
              <i class="bi bi-123"></i>
              <span>Each round: Choose a number from 0 to 100</span>
            </div>
            <div class="rule-item">
              <i class="bi bi-calculator"></i>
              <span>Calculation: Average of 5 numbers × 0.8</span>
            </div>
            <div class="rule-item">
              <i class="bi bi-trophy"></i>
              <span>Winner: Player closest to result gets +1 point</span>
            </div>
            <div class="rule-item">
              <i class="bi bi-dash-circle"></i>
              <span>Others: Get -1 point</span>
            </div>
            <div class="rule-item">
              <i class="bi bi-x-circle"></i>
              <span>Elimination: Reach -10 points and you're out!</span>
            </div>
            <div class="rule-item">
              <i class="bi bi-crown"></i>
              <span>Victory: Last player standing wins!</span>
            </div>
            <div class="rule-item">
              <i class="bi bi-clock"></i>
              <span>Time Limit: 1 minute per round</span>
            </div>
          </div>

          <!-- Create Form -->
          <form id="createGameForm" method="POST" action="game_handler.php">
            <input type="hidden" name="action" value="create_game">
            
            <div class="form-group">
              <label class="form-label">Your Name</label>
              <input type="text" name="host_name" class="form-control" placeholder="Enter your name" required maxlength="50">
            </div>

            <div class="form-group">
              <label class="form-label">Game Room Name</label>
              <input type="text" name="room_name" class="form-control" placeholder="Enter room name" required maxlength="100">
            </div>

            <button type="submit" class="btn-create">
              <i class="bi bi-plus-circle me-2"></i>Create Game Room
            </button>
          </form>
        </div>

      </div>
    </section>

  </main>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <script>
    // Initialize AOS
    AOS.init();

    // Form submission
    document.getElementById('createGameForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const formData = new FormData(this);
      
      fetch('game_handler.php', {
        method: 'POST',
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          window.location.href = `game_room.php?code=${data.room_code}&name=${encodeURIComponent(data.host_name)}`;
        } else {
          alert('Error: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred. Please try again.');
      });
    });
  </script>

</body>
</html>
