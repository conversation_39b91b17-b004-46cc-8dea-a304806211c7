<?php include 'db.php'; ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <style>
        :root {
            --default-color: #f1f3f5;
            --heading-color: #f4f7fa;
            --accent-color: #ceaf7f;
            --surface-color: #252a2b;
            --contrast-color: #ffffff;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: transparent;
            color: var(--default-color);
            margin: 0;
            padding: 1rem;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .chat-messages {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            max-height: 350px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .message {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 1.5rem 2rem;
            margin-bottom: 1.5rem;
            border-left: 6px solid var(--accent-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
            min-height: 80px;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.8rem;
        }

        .sender-name {
            color: var(--accent-color);
            font-weight: 700;
            font-size: 1.3rem;
            text-transform: capitalize;
            letter-spacing: 0.5px;
        }

        .message-time {
            color: rgba(241, 243, 245, 0.8);
            font-size: 1rem;
            font-weight: 600;
            background: rgba(206, 175, 127, 0.15);
            padding: 0.4rem 0.8rem;
            border-radius: 12px;
        }

        .message-content {
            color: var(--default-color);
            word-wrap: break-word;
            line-height: 1.8;
            font-size: 1.6rem;
            margin-top: 0.8rem;
            font-weight: 500;
        }

        .file-attachment {
            background: rgba(206, 175, 127, 0.1);
            border: 1px solid rgba(206, 175, 127, 0.3);
            border-radius: 12px;
            padding: 1rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .file-attachment:hover {
            background: rgba(206, 175, 127, 0.2);
            transform: translateY(-1px);
        }

        .file-icon {
            font-size: 2rem;
            color: var(--accent-color);
            min-width: 40px;
            text-align: center;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            color: var(--default-color);
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .file-size {
            color: rgba(241, 243, 245, 0.7);
            font-size: 0.8rem;
        }

        .file-download {
            background: var(--accent-color);
            color: var(--contrast-color);
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .file-download:hover {
            background: #d4b896;
            transform: translateY(-1px);
            color: var(--contrast-color);
            text-decoration: none;
        }

        .image-preview {
            max-width: 300px;
            max-height: 200px;
            border-radius: 12px;
            margin-top: 0.5rem;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .image-preview:hover {
            transform: scale(1.02);
        }

        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            cursor: pointer;
        }

        .image-modal img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
            border-radius: 8px;
        }

        .no-messages {
            text-align: center;
            color: rgba(241, 243, 245, 0.6);
            padding: 3rem 2rem;
            font-style: italic;
        }

        .message::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 0;
            bottom: 0;
            width: 6px;
            background: linear-gradient(135deg, var(--accent-color), #d4b896);
            border-radius: 3px;
        }

        .message:nth-child(even) {
            background: rgba(206, 175, 127, 0.05);
        }

        .message:nth-child(odd) {
            background: rgba(255, 255, 255, 0.08);
        }

        /* Custom scrollbar */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: var(--accent-color);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #d4b896;
        }

        /* Animation for new messages */
        .message {
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    <!-- Bootstrap Icons for better icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="chat-messages" id="chatMessages">
        <?php
        $room_code = $_GET['code'] ?? '';
        $result = $conn->query("SELECT * FROM messages WHERE room_code = '$room_code' ORDER BY timestamp ASC");

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $timestamp = new DateTime($row['timestamp']);
                $formatted_time = $timestamp->format('H:i');
                $formatted_date = $timestamp->format('M j');

                echo '<div class="message">';
                echo '<div class="message-header">';
                echo '<span class="sender-name">' . htmlspecialchars($row['sender_name']) . '</span>';
                echo '<span class="message-time">' . $formatted_time . '</span>';
                echo '</div>';

                // Display text message if exists
                if (!empty($row['message'])) {
                    echo '<div class="message-content">' . htmlspecialchars($row['message']) . '</div>';
                }

                // Display file attachment if exists
                if (!empty($row['file_path'])) {
                    $file_path = htmlspecialchars($row['file_path']);
                    $file_name = htmlspecialchars($row['file_name']);
                    $file_type = $row['file_type'];
                    $file_size = $row['file_size'];

                    // Format file size
                    $size_formatted = '';
                    if ($file_size < 1024) {
                        $size_formatted = $file_size . ' B';
                    } elseif ($file_size < 1024 * 1024) {
                        $size_formatted = round($file_size / 1024, 1) . ' KB';
                    } else {
                        $size_formatted = round($file_size / (1024 * 1024), 1) . ' MB';
                    }

                    // Check if it's an image
                    $image_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];
                    if (in_array($file_type, $image_types)) {
                        // Display image preview
                        echo '<div class="file-attachment">';
                        echo '<img src="' . $file_path . '" alt="' . $file_name . '" class="image-preview" onclick="openImageModal(\'' . $file_path . '\')">';
                        echo '<div class="file-info">';
                        echo '<div class="file-name">' . $file_name . '</div>';
                        echo '<div class="file-size">' . $size_formatted . '</div>';
                        echo '</div>';
                        echo '<a href="' . $file_path . '" download="' . $file_name . '" class="file-download">';
                        echo '<i class="bi bi-download"></i> Download';
                        echo '</a>';
                        echo '</div>';
                    } else {
                        // Display file attachment
                        $file_icon = 'bi-file-earmark';
                        if (strpos($file_type, 'pdf') !== false) $file_icon = 'bi-file-earmark-pdf';
                        elseif (strpos($file_type, 'word') !== false || strpos($file_type, 'document') !== false) $file_icon = 'bi-file-earmark-word';
                        elseif (strpos($file_type, 'excel') !== false || strpos($file_type, 'spreadsheet') !== false) $file_icon = 'bi-file-earmark-excel';
                        elseif (strpos($file_type, 'powerpoint') !== false || strpos($file_type, 'presentation') !== false) $file_icon = 'bi-file-earmark-ppt';
                        elseif (strpos($file_type, 'zip') !== false || strpos($file_type, 'rar') !== false) $file_icon = 'bi-file-earmark-zip';
                        elseif (strpos($file_type, 'audio') !== false) $file_icon = 'bi-file-earmark-music';
                        elseif (strpos($file_type, 'video') !== false) $file_icon = 'bi-file-earmark-play';

                        echo '<div class="file-attachment">';
                        echo '<i class="bi ' . $file_icon . ' file-icon"></i>';
                        echo '<div class="file-info">';
                        echo '<div class="file-name">' . $file_name . '</div>';
                        echo '<div class="file-size">' . $size_formatted . '</div>';
                        echo '</div>';
                        echo '<a href="' . $file_path . '" download="' . $file_name . '" class="file-download">';
                        echo '<i class="bi bi-download"></i> Download';
                        echo '</a>';
                        echo '</div>';
                    }
                }

                echo '</div>';
            }
        } else {
            echo '<div class="no-messages">';
            echo '<i class="bi bi-chat-dots" style="font-size: 3rem; color: var(--accent-color); margin-bottom: 1rem; display: block;"></i>';
            echo '<div style="font-size: 1.1rem; color: var(--default-color);">No messages yet. Start the conversation!</div>';
            echo '</div>';
        }
        ?>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <img id="modalImage" src="" alt="Full size image">
    </div>

    <script>
        // Image modal functions
        function openImageModal(imageSrc) {
            document.getElementById('imageModal').style.display = 'block';
            document.getElementById('modalImage').src = imageSrc;
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });
        // Auto-scroll to bottom
        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Scroll to bottom on load
        window.onload = function() {
            scrollToBottom();
        };

        // Store last message count to detect new messages
        let lastMessageCount = <?= $result->num_rows ?>;

        // Smooth refresh function without blinking
        function refreshMessages() {
            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const newDoc = parser.parseFromString(html, 'text/html');
                    const newMessages = newDoc.getElementById('chatMessages');
                    const currentMessages = document.getElementById('chatMessages');

                    if (newMessages && currentMessages) {
                        const newMessageCount = newMessages.querySelectorAll('.message').length;
                        if (newMessageCount !== lastMessageCount) {
                            currentMessages.innerHTML = newMessages.innerHTML;
                            lastMessageCount = newMessageCount;
                            scrollToBottom();
                        }
                    }
                })
                .catch(error => {
                    console.log('Refresh error:', error);
                });
        }

        // Start smooth refresh every 3 seconds
        setInterval(refreshMessages, 3000);

        // Check for new messages after refresh
        window.addEventListener('load', function() {
            const currentMessageCount = document.querySelectorAll('.message').length;
            if (currentMessageCount > lastMessageCount) {
                scrollToBottom();
            }
        });
    </script>
</body>
</html>
