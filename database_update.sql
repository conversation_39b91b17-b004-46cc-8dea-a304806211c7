-- Database update for file sharing functionality
-- Run this SQL to add file sharing capabilities to your chat system

-- Add file_path column to existing messages table
ALTER TABLE messages ADD COLUMN file_path VARCHAR(255) DEFAULT NULL;
ALTER TABLE messages ADD COLUMN file_name VARCHAR(255) DEFAULT NULL;
ALTER TABLE messages ADD COLUMN file_type VARCHAR(50) DEFAULT NULL;
ALTER TABLE messages ADD COLUMN file_size INT DEFAULT NULL;

-- <PERSON><PERSON> uploads directory structure
-- You need to create these folders manually:
-- uploads/
-- uploads/images/
-- uploads/documents/
-- uploads/other/

-- Update messages table to allow NULL message content when file is shared
ALTER TABLE messages MODIFY COLUMN message TEXT NULL;

-- Add index for better performance
ALTER TABLE messages ADD INDEX idx_room_timestamp (room_code, timestamp);
ALTER TABLE messages ADD INDEX idx_file_type (file_type);

-- Optional: Create a separate files table for better organization (alternative approach)
CREATE TABLE IF NOT EXISTS chat_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT,
    file_path VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
);
