<!DOCTYPE html>
<html>
<head>
    <title>Test Response Format - QuickMeet</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .test-section { border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; }
        .response-box { border: 2px solid #007bff; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Response Format Test</h1>
    <p>This tool shows exactly what the game handler is returning to help debug the "Invalid response format" error.</p>
    
    <div class="test-section">
        <h3>1. Test Basic Game Handler Response</h3>
        <button onclick="testBasicResponse()">Test Basic Response</button>
        <div id="basicResponse"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test Submit Number Response</h3>
        <p>First, let's set up a test room and player:</p>
        <button onclick="setupTestRoom()">Setup Test Room</button>
        <div id="setupResult"></div>
        
        <p>Then test number submission:</p>
        <button onclick="testSubmitResponse()">Test Submit Number</button>
        <div id="submitResponse"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Raw Response Analysis</h3>
        <button onclick="analyzeResponse()">Analyze Raw Response</button>
        <div id="analysisResult"></div>
    </div>

    <script>
        async function testBasicResponse() {
            const result = document.getElementById('basicResponse');
            result.innerHTML = '<p>Testing basic response...</p>';
            
            try {
                const response = await fetch('game_handler.php?action=test');
                const responseText = await response.text();
                
                result.innerHTML = `
                    <div class="response-box">
                        <h4>Response Details:</h4>
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Content-Type:</strong> ${response.headers.get('content-type')}</p>
                        <p><strong>Response Length:</strong> ${responseText.length} characters</p>
                        
                        <h4>Raw Response:</h4>
                        <pre>${responseText}</pre>
                        
                        <h4>JSON Parse Test:</h4>
                        ${testJsonParse(responseText)}
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">Fetch Error: ${error.message}</div>`;
            }
        }
        
        async function setupTestRoom() {
            const result = document.getElementById('setupResult');
            result.innerHTML = '<p>Setting up test room...</p>';
            
            try {
                // Create room
                const formData = new FormData();
                formData.append('action', 'create_game');
                formData.append('host_name', 'TestUser');
                formData.append('room_name', 'Test Room');
                
                const response = await fetch('game_handler.php', {
                    method: 'POST',
                    body: formData
                });
                
                const responseText = await response.text();
                
                result.innerHTML = `
                    <div class="response-box">
                        <h4>Room Creation Response:</h4>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <pre>${responseText}</pre>
                        ${testJsonParse(responseText)}
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">Setup Error: ${error.message}</div>`;
            }
        }
        
        async function testSubmitResponse() {
            const result = document.getElementById('submitResponse');
            result.innerHTML = '<p>Testing submit number response...</p>';
            
            try {
                const formData = new FormData();
                formData.append('action', 'submit_number');
                formData.append('room_code', 'TEST01');
                formData.append('player_name', 'TestUser');
                formData.append('chosen_number', '42');
                
                const response = await fetch('game_handler.php', {
                    method: 'POST',
                    body: formData
                });
                
                const responseText = await response.text();
                
                result.innerHTML = `
                    <div class="response-box">
                        <h4>Submit Number Response:</h4>
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Content-Type:</strong> ${response.headers.get('content-type')}</p>
                        <p><strong>Response Length:</strong> ${responseText.length} characters</p>
                        
                        <h4>Raw Response:</h4>
                        <pre>${responseText}</pre>
                        
                        <h4>JSON Parse Test:</h4>
                        ${testJsonParse(responseText)}
                        
                        <h4>Character Analysis:</h4>
                        <pre>${analyzeCharacters(responseText)}</pre>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">Submit Error: ${error.message}</div>`;
            }
        }
        
        async function analyzeResponse() {
            const result = document.getElementById('analysisResult');
            result.innerHTML = '<p>Analyzing response format...</p>';
            
            try {
                // Test multiple endpoints
                const tests = [
                    { name: 'Basic Test', url: 'game_handler.php?action=test' },
                    { name: 'Invalid Action', url: 'game_handler.php?action=invalid' },
                    { name: 'No Action', url: 'game_handler.php' }
                ];
                
                let analysis = '<div class="response-box"><h4>Response Analysis:</h4>';
                
                for (const test of tests) {
                    try {
                        const response = await fetch(test.url);
                        const text = await response.text();
                        
                        analysis += `
                            <h5>${test.name}:</h5>
                            <p>Status: ${response.status}, Length: ${text.length}</p>
                            <pre>${text.substring(0, 200)}${text.length > 200 ? '...' : ''}</pre>
                        `;
                    } catch (e) {
                        analysis += `<p>${test.name}: Error - ${e.message}</p>`;
                    }
                }
                
                analysis += '</div>';
                result.innerHTML = analysis;
                
            } catch (error) {
                result.innerHTML = `<div class="error">Analysis Error: ${error.message}</div>`;
            }
        }
        
        function testJsonParse(text) {
            try {
                const parsed = JSON.parse(text);
                return `<div class="success">✅ Valid JSON: ${JSON.stringify(parsed, null, 2)}</div>`;
            } catch (e) {
                return `<div class="error">❌ Invalid JSON: ${e.message}</div>`;
            }
        }
        
        function analyzeCharacters(text) {
            const chars = text.split('').map((char, index) => {
                const code = char.charCodeAt(0);
                if (code < 32 || code > 126) {
                    return `[${index}]: ${code} (${char === '\n' ? '\\n' : char === '\r' ? '\\r' : char === '\t' ? '\\t' : 'special'})`;
                }
                return null;
            }).filter(Boolean);
            
            return chars.length > 0 ? 
                `Special characters found:\n${chars.join('\n')}` : 
                'No special characters found';
        }
    </script>
</body>
</html>
