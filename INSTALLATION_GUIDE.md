# 🚀 QuickMeet Installation Guide

Complete setup guide for QuickMeet chat application with file sharing functionality.

## 📋 Prerequisites

- **Web Server** (Apache/Nginx)
- **PHP 7.4+** with mysqli extension
- **MySQL 5.7+** or **MariaDB 10.2+**
- **Write permissions** for uploads directory

## 🛠️ Installation Steps

### Step 1: Database Setup

#### Option A: Automatic Setup (Recommended)
1. Open your browser and go to: `http://your-domain/setup_database.php`
2. Click "Setup QuickMeet Database" button
3. Wait for completion message

#### Option B: Manual Setup
1. Open phpMyAdmin or MySQL command line
2. Import the file: `quickmeet_complete_database.sql`
3. Run the setup script: `setup_uploads.php`

### Step 2: Configure Database Connection

Edit `db.php` file with your database credentials:

```php
$servername = "localhost";
$username = "root";
$password = "your_mysql_password";
$dbname = "quickmeet_chat";
```

### Step 3: Set Directory Permissions

Make sure these directories are writable:
```bash
chmod 755 uploads/
chmod 755 uploads/images/
chmod 755 uploads/documents/
chmod 755 uploads/other/
```

### Step 4: Test Installation

1. Visit: `http://your-domain/index.php`
2. Create a test room
3. Try sending messages and files
4. Check admin panel: `http://your-domain/admin.php`

## 📊 Database Structure

### Tables Created:

#### `rooms` - Chat room information
- `id` - Primary key
- `room_code` - Unique 6-digit room code
- `name` - Host name
- `phone` - Host phone number
- `age` - Host age
- `gender` - Host gender (male/female/other)
- `password` - Room password
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

#### `messages` - Chat messages and files
- `id` - Primary key
- `room_code` - Foreign key to rooms
- `sender_name` - Message sender name
- `message` - Text message (nullable)
- `file_path` - Uploaded file path (nullable)
- `file_name` - Original file name (nullable)
- `file_type` - MIME type (nullable)
- `file_size` - File size in bytes (nullable)
- `timestamp` - Message timestamp

#### `chat_files` - File management (optional)
- `id` - Primary key
- `message_id` - Foreign key to messages
- `file_path` - File storage path
- `file_name` - Original filename
- `file_type` - MIME type
- `file_size` - File size
- `uploaded_at` - Upload timestamp

#### `admin_logs` - Admin activity tracking
- `id` - Primary key
- `action` - Action performed
- `details` - Action details
- `ip_address` - Admin IP
- `user_agent` - Browser info
- `created_at` - Action timestamp

## 🔧 Configuration Options

### File Upload Settings

Edit `upload_file.php` to modify:
- **Max file size**: Currently 10MB
- **Allowed file types**: Images, documents, archives, media
- **Upload directories**: Organized by file type

### Security Settings

The `.htaccess` file in uploads/ directory:
- Prevents direct file access
- Blocks PHP execution
- Allows only specific file types

### Admin Access

Default admin password: `admin2550`
Change in `admin.php` if needed.

## 🎯 Features Included

### Chat Features
- ✅ Real-time messaging
- ✅ Room-based chat system
- ✅ File and image sharing
- ✅ WhatsApp integration
- ✅ Mobile responsive design

### File Sharing
- ✅ Image preview with modal
- ✅ Document sharing
- ✅ File type icons
- ✅ Download functionality
- ✅ Size formatting

### Admin Dashboard
- ✅ Room management
- ✅ Message monitoring
- ✅ SQL query tool
- ✅ Statistics dashboard
- ✅ Data cleanup tools

## 🔍 Troubleshooting

### Common Issues:

#### Database Connection Error
- Check MySQL credentials in `db.php`
- Ensure MySQL service is running
- Verify database exists

#### File Upload Not Working
- Check directory permissions (755)
- Verify PHP upload settings
- Check file size limits

#### Images Not Displaying
- Ensure uploads directory is accessible
- Check .htaccess configuration
- Verify file paths in database

#### Admin Panel Access Denied
- Default password: `admin2550`
- Check session configuration
- Clear browser cache

## 📱 Mobile Compatibility

QuickMeet is fully responsive and works on:
- ✅ Desktop browsers
- ✅ Mobile phones
- ✅ Tablets
- ✅ Touch devices

## 🔒 Security Features

- ✅ SQL injection prevention
- ✅ File type validation
- ✅ Directory protection
- ✅ Session management
- ✅ XSS protection

## 📞 Support

For issues or questions:
- **Phone**: +91 7028844513
- **Email**: <EMAIL>

## 🎉 You're Ready!

Your QuickMeet application is now fully set up with:
- Complete database structure
- File sharing functionality
- Admin dashboard
- Security measures
- Mobile responsiveness

Visit your homepage and start creating chat rooms! 🚀
