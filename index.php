<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>QuickMeet - Instant Group Chat Rooms</title>
  <meta name="description" content="QuickMeet - Advanced real-time chat platform with secure room-based messaging, file sharing (images, documents, media), and seamless WhatsApp integration. Create private chat rooms with unique codes, share files up to 10MB, enjoy admin dashboard monitoring, and connect instantly without registration. Built with PHP, MySQL, and modern web technologies for optimal performance and security.">
  <meta name="keywords" content="QuickMeet, real-time chat platform, secure messaging, room-based chat, file sharing system, WhatsApp integration, private chat rooms, document sharing, image sharing, admin dashboard, PHP chat application, MySQL database, web-based messaging, instant communication, group collaboration, <PERSON><PERSON><PERSON>, Dayanand Jagadale, Shrinivas Solapur, Saurabh Agalve, chat room codes, mobile responsive, cross-platform messaging">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="/assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    /* Chat App Specific Styles */
    .hero-section {
      min-height: 100vh;
      display: flex;
      align-items: center;
      position: relative;
      padding: 120px 0 80px 0;
    }

    .feature-card {
      background: var(--surface-color);
      border-radius: 15px;
      padding: 2rem;
      text-align: center;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid rgba(206, 175, 127, 0.1);
      height: 100%;
    }

    .feature-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(206, 175, 127, 0.1);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1.5rem;
      font-size: 2rem;
      color: var(--contrast-color);
    }

    .cta-button {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 15px 40px;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.1rem;
      transition: all 0.3s ease;
      border: none;
      display: inline-block;
      margin: 10px;
    }

    .cta-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 30px rgba(206, 175, 127, 0.3);
      color: var(--contrast-color);
    }

    .cta-button.secondary {
      background: transparent;
      border: 2px solid var(--accent-color);
      color: var(--accent-color);
    }

    .cta-button.secondary:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .stats-section {
      background: var(--surface-color);
      border-radius: 20px;
      padding: 3rem 2rem;
      margin: 4rem 0;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: 3rem;
      font-weight: 700;
      color: var(--accent-color);
      display: block;
    }

    .stat-label {
      color: var(--default-color);
      font-size: 1.1rem;
      margin-top: 0.5rem;
    }

    .hero-main-image {
      max-width: 100%;
      height: auto;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .hero-main-image:hover {
      transform: translateY(-10px);
      box-shadow: 0 30px 80px rgba(206, 175, 127, 0.2);
    }

    @media (max-width: 991px) {
      .hero-main-image {
        margin-top: 2rem;
        max-width: 80%;
      }
    }
  </style>
</head>

<body class="index-page">

  <!-- Header -->
  <header id="header" class="header d-flex align-items-center sticky-top">
    <div class="container position-relative d-flex align-items-center justify-content-between">

      <a href="index.php" class="logo d-flex align-items-center me-auto me-xl-0">
        <img src="assets/img/bg/chat.png" alt="QuickMeet Logo" style="height: 40px; margin-right: 0.5rem;">
        <h1 class="sitename">QuickMeet</h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="#hero" class="active">Home</a></li>
          <li><a href="#features">Features</a></li>
          <li><a href="#how-it-works">How It Works</a></li>
          <li><a href="https://quickf.free.nf/" target="_blank">Quick</a></li>
          <li><a href="admin.php">Admin</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

      <div class="header-actions">
        <a class="cta-button" href="create_room.php">Create Room</a>
      </div>

    </div>
  </header>

  <main class="main">

    <!-- Hero Section -->
    <section id="hero" class="hero-section">
      <div class="container" data-aos="fade-up">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <div class="hero-content">
              <h1 class="display-4 fw-bold mb-4">
                Connect Instantly with
                <span style="color: var(--accent-color);">QuickMeet</span>
              </h1>
              <p class="lead mb-4">
                Create secure chat rooms in seconds. Share room codes with friends via WhatsApp and start chatting immediately. No registration required!
              </p>
              <div class="hero-buttons">
                <a href="create_room.php" class="cta-button">
                  <i class="bi bi-plus-circle me-2"></i>Create Chat Room
                </a>
                <a href="join_room.php" class="cta-button secondary">
                  <i class="bi bi-door-open me-2"></i>Join Chat Room
                </a>
              </div>

              <!-- Game Buttons -->
              <div class="hero-buttons" style="margin-top: 1rem;">
                <a href="create_game.php" class="cta-button" style="background: linear-gradient(135deg, #28a745, #20c997);">
                  <i class="bi bi-controller me-2"></i>Create Math Game
                </a>
                <a href="join_game.php" class="cta-button secondary">
                  <i class="bi bi-puzzle me-2"></i>Join Math Game
                </a>
              </div>
            </div>
          </div>
          <div class="col-lg-6" data-aos="fade-left" data-aos-delay="200">
            <div class="hero-image text-center">
              <img src="/assets/mainpage.jpg" alt="QuickMeet - Group Chat Illustration" class="img-fluid hero-main-image">
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
      <div class="container" data-aos="fade-up">
        <div class="section-header text-center mb-5">
          <h2>Why Choose QuickMeet?</h2>
          <p>Simple, secure, and instant group communication</p>
        </div>

        <div class="row g-4">
          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="bi bi-lightning-charge"></i>
              </div>
              <h4>Instant Setup</h4>
              <p>Create chat rooms in seconds. No lengthy registration process or email verification required.</p>
            </div>
          </div>

          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="bi bi-whatsapp"></i>
              </div>
              <h4>WhatsApp Integration</h4>
              <p>Share room codes directly via WhatsApp with built-in invitation messages and links.</p>
            </div>
          </div>

          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="bi bi-shield-check"></i>
              </div>
              <h4>Password Protected</h4>
              <p>Every room is secured with a password. Only invited members can join your conversations.</p>
            </div>
          </div>

          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="bi bi-clock-history"></i>
              </div>
              <h4>Real-time Chat</h4>
              <p>Messages appear instantly. Stay connected with live conversations and immediate responses.</p>
            </div>
          </div>

          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="bi bi-people"></i>
              </div>
              <h4>Group Friendly</h4>
              <p>Perfect for family groups, friend circles, work teams, or any group communication needs.</p>
            </div>
          </div>

          <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="bi bi-device-hdd"></i>
              </div>
              <h4>No App Required</h4>
              <p>Works directly in your web browser. No downloads, installations, or app store visits needed.</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-5 light-background">
      <div class="container" data-aos="fade-up">
        <div class="section-header text-center mb-5">
          <h2>How It Works</h2>
          <p>Get started in 3 simple steps</p>
        </div>

        <div class="row g-4">
          <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
            <div class="step-card text-center">
              <div class="step-number">1</div>
              <div class="step-icon">
                <i class="bi bi-plus-circle"></i>
              </div>
              <h4>Create Room</h4>
              <p>Fill in your details and create a secure chat room. You'll get a unique 6-digit room code.</p>
            </div>
          </div>

          <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
            <div class="step-card text-center">
              <div class="step-number">2</div>
              <div class="step-icon">
                <i class="bi bi-share"></i>
              </div>
              <h4>Share Code</h4>
              <p>Share your room code and password with friends via WhatsApp or any messaging platform.</p>
            </div>
          </div>

          <div class="col-lg-4" data-aos="fade-up" data-aos-delay="300">
            <div class="step-card text-center">
              <div class="step-number">3</div>
              <div class="step-icon">
                <i class="bi bi-chat-dots"></i>
              </div>
              <h4>Start Chatting</h4>
              <p>Once friends join using the code, start your group conversation immediately!</p>
            </div>
          </div>
        </div>

        <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="400">
          <a href="create_room.php" class="cta-button">
            <i class="bi bi-rocket-takeoff me-2"></i>Get Started Now
          </a>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-5">
      <div class="container" data-aos="fade-up">
        <div class="stats-section">
          <div class="row">
            <div class="col-lg-3 col-md-6">
              <div class="stat-item">
                <span class="stat-number" id="counter1">4,521</span>
                <div class="stat-label">Rooms Created</div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="stat-item">
                <span class="stat-number" id="counter2">27,834</span>
                <div class="stat-label">Messages Sent</div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="stat-item">
                <span class="stat-number" id="counter3">12,456</span>
                <div class="stat-label">Hours Online</div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="stat-item">
                <span class="stat-number" id="counter4">99.9%</span>
                <div class="stat-label">Uptime</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-5 dark-background">
      <div class="container text-center" data-aos="fade-up">
        <h2 class="mb-4">Ready to Connect?</h2>
        <p class="lead mb-4">Join thousands of users who trust ChatConnect for their group communication needs.</p>
        <div class="cta-buttons">
          <a href="create_room.php" class="cta-button me-3">
            <i class="bi bi-plus-circle me-2"></i>Create Your Room
          </a>
          <a href="join_room.php" class="cta-button secondary">
            <i class="bi bi-door-open me-2"></i>Join Existing Room
          </a>
        </div>
      </div>
    </section>

  </main>

  <!-- Footer -->
  <footer id="footer" class="footer dark-background">
    <div class="container">
      <div class="row gy-3">
        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-geo-alt icon"></i>
          <div class="address">
            <h4>Address</h4>
            <p>QuickMeet HQ<br>Digital Communication Center<br></p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-telephone icon"></i>
          <div>
            <h4>Contact</h4>
            <p>
              <strong>Phone:</strong> <span>+91 7028844513</span><br>
              <strong>Email:</strong> <span><EMAIL></span><br>
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-clock icon"></i>
          <div>
            <h4>Available</h4>
            <p>
              <strong>24/7 Service</strong><br>
              Always online and ready to connect you with friends and family.
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6">
          <h4>Follow Us</h4>
          <div class="social-links d-flex">
            <a href="#" class="twitter"><i class="bi bi-twitter-x"></i></a>
            <a href="#" class="facebook"><i class="bi bi-facebook"></i></a>
            <a href="#" class="instagram"><i class="bi bi-instagram"></i></a>
            <a href="#" class="linkedin"><i class="bi bi-linkedin"></i></a>
          </div>
        </div>
      </div>
    </div>

    <div class="container copyright text-center mt-4">
      <p>© <span>Copyright</span> <strong class="px-1 sitename">QuickMeet</strong> <span>All Rights Reserved</span></p>
    </div>
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <script>
    // Custom counter animation
    function animateCounter(elementId, start, end, duration, isPercentage = false, hasComma = false) {
      const element = document.getElementById(elementId);
      const startTime = performance.now();

      function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = start + (end - start) * easeOutQuart;

        let displayValue;
        if (isPercentage) {
          displayValue = current.toFixed(1) + '%';
        } else if (hasComma) {
          displayValue = Math.floor(current).toLocaleString();
        } else {
          displayValue = Math.floor(current).toString();
        }

        element.textContent = displayValue;

        if (progress < 1) {
          requestAnimationFrame(updateCounter);
        }
      }

      requestAnimationFrame(updateCounter);
    }

    // Initialize counters when page loads
    window.addEventListener('load', function() {
      // Add a small delay to make the animation more noticeable
      setTimeout(() => {
        // Reset counters to 0 first
        document.getElementById('counter1').textContent = '0';
        document.getElementById('counter2').textContent = '0';
        document.getElementById('counter3').textContent = '0';
        document.getElementById('counter4').textContent = '0%';

        // Start animations
        setTimeout(() => {
          animateCounter('counter1', 0, 4521, 2000, false, true);    // Rooms Created
          animateCounter('counter2', 0, 27834, 2500, false, true);   // Messages Sent
          animateCounter('counter3', 0, 12456, 2200, false, true);   // Hours Online
          animateCounter('counter4', 0, 99.9, 1800, true, false);    // Uptime %
        }, 500);
      }, 1000);
    });

    // Also trigger animation when stats section comes into view
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Trigger animation when stats section is visible
          setTimeout(() => {
            document.getElementById('counter1').textContent = '0';
            document.getElementById('counter2').textContent = '0';
            document.getElementById('counter3').textContent = '0';
            document.getElementById('counter4').textContent = '0%';

            setTimeout(() => {
              animateCounter('counter1', 0, 4521, 2000, false, true);
              animateCounter('counter2', 0, 27834, 2500, false, true);
              animateCounter('counter3', 0, 12456, 2200, false, true);
              animateCounter('counter4', 0, 99.9, 1800, true, false);
            }, 200);
          }, 500);

          observer.unobserve(entry.target);
        }
      });
    });

    // Observe the stats section
    document.addEventListener('DOMContentLoaded', function() {
      const statsSection = document.querySelector('.stats-section');
      if (statsSection) {
        observer.observe(statsSection);
      }
    });
  </script>

  <style>
    /* Additional step card styles */
    .step-card {
      position: relative;
      padding: 2rem;
      background: var(--surface-color);
      border-radius: 15px;
      transition: transform 0.3s ease;
      border: 1px solid rgba(206, 175, 127, 0.1);
    }

    .step-card:hover {
      transform: translateY(-5px);
    }

    .step-number {
      position: absolute;
      top: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 40px;
      background: var(--accent-color);
      color: var(--contrast-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1.2rem;
    }

    .step-icon {
      font-size: 3rem;
      color: var(--accent-color);
      margin: 1rem 0;
    }

    .step-card h4 {
      margin-bottom: 1rem;
    }
  </style>

</body>
</html>