# 🎮 QuickMeet - Mathematical Strategy Game

An exciting multiplayer mathematical strategy game for 2-5 players. Challenge your strategic thinking by choosing numbers and getting closest to the calculated target!

## 🎯 Game Overview

**QuickMeet** is a browser-based mathematical strategy game where players compete by choosing numbers between 0-100. The winner of each round is the player whose number is closest to **(average × 0.8)**.

### 🎮 Game Features

- 🎲 **2-5 Players** - Flexible player count
- ⏰ **1-Minute Rounds** - Fast-paced gameplay
- 🧮 **Strategic Math** - Calculate optimal numbers
- 🏆 **Competitive Scoring** - Winner +1, Others -1
- 💀 **Elimination System** - Eliminated at -10 points
- 🌐 **Browser-Based** - No downloads required

## 🚀 Quick Start

1. **Setup**: Follow `GAME_INSTALLATION.md`
2. **Create**: Game room with unique code
3. **Invite**: 1-4 friends to join
4. **Play**: Choose numbers strategically
5. **Win**: Be the last player standing!

## 📁 Project Structure

```
QuickMeet/
├── 🏠 Core Files
│   ├── index.php          # Game homepage
│   ├── admin.php          # Admin dashboard
│   └── db.php             # Database connection
│
├── 🎮 Game System
│   ├── create_game.php    # Create game room
│   ├── join_game.php      # Join game room
│   ├── game_room.php      # Game interface
│   └── game_handler.php   # Game logic
│
├── 🛠️ Setup & Config
│   ├── setup_game_db.php  # Database setup
│   ├── check_game_db.php  # Database checker
│   ├── game_setup.sql     # Database schema
│   └── GAME_INSTALLATION.md
│
└── 📁 Assets
    ├── css/               # Stylesheets
    ├── js/                # JavaScript
    ├── img/               # Images
    └── vendor/            # Libraries
```

## 🎯 How to Play

### Game Rules

1. **Join**: 2-5 players join a game room
2. **Choose**: Each player picks a number (0-100)
3. **Calculate**: System finds average × 0.8 = target
4. **Winner**: Player closest to target gets +1 point
5. **Others**: All other players get -1 point
6. **Eliminate**: Players eliminated at -10 points
7. **Victory**: Last player standing wins!

### Example Round

```
🎮 3 Players Game:
Player A chooses: 50
Player B chooses: 60
Player C chooses: 70

📊 Calculation:
Average = (50 + 60 + 70) ÷ 3 = 60
Target = 60 × 0.8 = 48

🏆 Results:
Player A: |50 - 48| = 2 (Winner! +1 point)
Player B: |60 - 48| = 12 (-1 point)
Player C: |70 - 48| = 22 (-1 point)
```

## 🛠️ Installation

See detailed setup instructions in `GAME_INSTALLATION.md`

### Quick Setup

1. Upload files to web server
2. Configure database in `db.php`
3. Run `setup_game_db.php`
4. Visit `index.php` to start playing!

## 🔧 Admin Features

- **URL**: `/admin.php`
- **Password**: `admin2550`
- **Features**:
  - View game statistics
  - Monitor active games
  - Delete all game data
  - Database management

## 👥 Development Team

- **Mohasin Shaikh** - Lead Developer
- **Dayanand Jagadale** - Backend Developer
- **Shrinivas Solapur** - Frontend Developer
- **Saurabh Agalve** - Database Developer

## 🎮 Game URLs

- **Homepage**: `index.php`
- **Create Game**: `create_game.php`
- **Join Game**: `join_game.php`
- **Game Room**: `game_room.php`
- **Admin Panel**: `admin.php`

## 🎯 Strategic Tips

1. **Observe Patterns** - Watch other players' strategies
2. **Think Psychology** - Predict what others might choose
3. **Calculate Ranges** - Estimate likely averages
4. **Adapt Strategy** - Change approach based on game state
5. **Time Management** - Use full 60 seconds wisely

## 🏆 Scoring System

- **Round Winner**: +1 point
- **Round Losers**: -1 point each
- **Elimination**: At -10 points
- **Game Winner**: Last player standing

## 🎉 Have Fun!

Challenge your friends to this exciting mathematical strategy game and see who has the best strategic thinking skills!

**Good luck and may the best strategist win!** 🎮🎯🏆
