-- Quick<PERSON><PERSON>t Mathematical Game Database Setup
-- Run this SQL to create the game tables

-- Game rooms table
CREATE TABLE IF NOT EXISTS game_rooms (
    room_code VARCHAR(10) PRIMARY KEY,
    room_name VARCHAR(100),
    host_name VARCHAR(50),
    status ENUM('waiting', 'playing', 'break', 'finished') DEFAULT 'waiting',
    current_round INT DEFAULT 0,
    round_start_time TIMESTAMP NULL,
    max_players INT DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Game players table
CREATE TABLE IF NOT EXISTS game_players (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_code VARCHAR(10),
    player_name VARCHAR(50),
    player_id VARCHAR(20),
    score INT DEFAULT 0,
    status ENUM('active', 'eliminated', 'disconnected') DEFAULT 'active',
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_code) REFERENCES game_rooms(room_code) ON DELETE CASCADE,
    UNIQUE KEY unique_player_room (room_code, player_name)
);

-- Game rounds table
CREATE TABLE IF NOT EXISTS game_rounds (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_code VARCHAR(10),
    round_number INT,
    player_name VARCHAR(50),
    chosen_number INT,
    is_submitted BOOLEAN DEFAULT FALSE,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_code) REFERENCES game_rooms(room_code) ON DELETE CASCADE,
    UNIQUE KEY unique_round_player (room_code, round_number, player_name)
);

-- Game results table (for round results)
CREATE TABLE IF NOT EXISTS game_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_code VARCHAR(10),
    round_number INT,
    average_number DECIMAL(5,2),
    target_number DECIMAL(5,2),
    winner_name VARCHAR(50),
    rules_applied VARCHAR(255) DEFAULT '',
    rule_messages TEXT,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_code) REFERENCES game_rooms(room_code) ON DELETE CASCADE
);

-- Chat messages for game rooms (extends existing messages table)
ALTER TABLE messages ADD COLUMN IF NOT EXISTS game_room BOOLEAN DEFAULT FALSE;
ALTER TABLE messages ADD COLUMN IF NOT EXISTS message_type ENUM('chat', 'system', 'game') DEFAULT 'chat';

-- Indexes for better performance
CREATE INDEX idx_game_rooms_status ON game_rooms(status);
CREATE INDEX idx_game_players_room ON game_players(room_code);
CREATE INDEX idx_game_players_status ON game_players(status);
CREATE INDEX idx_game_rounds_room_round ON game_rounds(room_code, round_number);
CREATE INDEX idx_messages_game_room ON messages(room_code, game_room);
