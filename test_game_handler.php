<!DOCTYPE html>
<html>
<head>
    <title>Test Game Handler - QuickMeet</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .test-section { border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Game Handler Test</h1>
    
    <?php
    echo "<h2>1. Database Connection Test</h2>";
    include 'db.php';
    
    if (!$conn) {
        echo "<div class='error'>❌ Database connection failed</div>";
        exit;
    } else {
        echo "<div class='success'>✅ Database connected</div>";
    }
    
    echo "<h2>2. Game Handler Basic Test</h2>";
    $test_url = "http://localhost:3000/game_handler.php?action=test";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $test_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<div class='test-section'>";
    echo "<p><strong>URL:</strong> $test_url</p>";
    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
    echo "<p><strong>Response:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    echo "</div>";
    
    if ($http_code === 200) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "<div class='success'>✅ Game handler is working</div>";
        } else {
            echo "<div class='error'>❌ Game handler returned error</div>";
        }
    } else {
        echo "<div class='error'>❌ Game handler HTTP error: $http_code</div>";
    }
    
    echo "<h2>3. Test Room Setup</h2>";
    $test_room = '3J1H26';
    $test_player = 'TestUser';
    
    // Check if room exists
    $room_check = $conn->prepare("SELECT * FROM game_rooms WHERE room_code = ?");
    $room_check->bind_param("s", $test_room);
    $room_check->execute();
    $room_result = $room_check->get_result();
    
    if ($room_result->num_rows > 0) {
        $room = $room_result->fetch_assoc();
        echo "<div class='success'>✅ Room $test_room exists</div>";
        echo "<pre>" . print_r($room, true) . "</pre>";
    } else {
        echo "<div class='error'>❌ Room $test_room does not exist</div>";
        echo "<p>Creating test room...</p>";
        
        $create_room = $conn->prepare("INSERT INTO game_rooms (room_code, room_name, host_name, status) VALUES (?, 'Test Room', ?, 'waiting')");
        $create_room->bind_param("ss", $test_room, $test_player);
        
        if ($create_room->execute()) {
            echo "<div class='success'>✅ Test room created</div>";
        } else {
            echo "<div class='error'>❌ Failed to create room: " . $conn->error . "</div>";
        }
    }
    
    // Check if player exists
    $player_check = $conn->prepare("SELECT * FROM game_players WHERE room_code = ? AND player_name = ?");
    $player_check->bind_param("ss", $test_room, $test_player);
    $player_check->execute();
    $player_result = $player_check->get_result();
    
    if ($player_result->num_rows > 0) {
        echo "<div class='success'>✅ Player $test_player exists in room</div>";
    } else {
        echo "<div class='error'>❌ Player $test_player not in room</div>";
        echo "<p>Adding player to room...</p>";
        
        $add_player = $conn->prepare("INSERT INTO game_players (room_code, player_name, player_id, score, status) VALUES (?, ?, ?, 0, 'active')");
        $player_id = uniqid();
        $add_player->bind_param("sss", $test_room, $test_player, $player_id);
        
        if ($add_player->execute()) {
            echo "<div class='success'>✅ Player added to room</div>";
        } else {
            echo "<div class='error'>❌ Failed to add player: " . $conn->error . "</div>";
        }
    }
    
    echo "<h2>4. Test Game State</h2>";
    $state_url = "http://localhost:3000/game_handler.php?action=get_game_state&room_code=" . urlencode($test_room);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $state_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<div class='test-section'>";
    echo "<p><strong>URL:</strong> $state_url</p>";
    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
    echo "<p><strong>Response:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    echo "</div>";
    
    echo "<h2>5. Test Number Submission</h2>";
    echo "<div class='test-section'>";
    echo "<p>Testing number submission for room $test_room...</p>";
    
    // First start the game
    $start_data = [
        'action' => 'start_game',
        'room_code' => $test_room,
        'player_name' => $test_player
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost:3000/game_handler.php");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($start_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $start_response = curl_exec($ch);
    curl_close($ch);
    
    echo "<p><strong>Start Game Response:</strong></p>";
    echo "<pre>" . htmlspecialchars($start_response) . "</pre>";
    
    // Then try to submit a number
    $submit_data = [
        'action' => 'submit_number',
        'room_code' => $test_room,
        'player_name' => $test_player,
        'chosen_number' => 42
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost:3000/game_handler.php");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($submit_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $submit_response = curl_exec($ch);
    curl_close($ch);
    
    echo "<p><strong>Submit Number Response:</strong></p>";
    echo "<pre>" . htmlspecialchars($submit_response) . "</pre>";
    echo "</div>";
    
    echo "<h2>6. Quick Links</h2>";
    echo "<p>";
    echo "<a href='game_room.php?code=$test_room&name=$test_player' class='btn'>🎮 Test Game Room</a>";
    echo "<a href='debug_game_room.php?code=$test_room&name=$test_player' class='btn'>🔍 Debug Game Room</a>";
    echo "<a href='quick_setup.php' class='btn'>🛠️ Quick Setup</a>";
    echo "</p>";
    
    $conn->close();
    ?>
    
    <hr>
    <p style="text-align: center; color: #666;">
        <small>QuickMeet Game Handler Test Tool</small>
    </p>
</body>
</html>
