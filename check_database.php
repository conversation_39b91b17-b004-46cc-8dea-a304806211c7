<!DOCTYPE html>
<html>
<head>
    <title>Database Check - QuickMeet</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🔍 Database Check</h1>
    
    <?php
    echo "<h2>1. Database Connection</h2>";
    
    try {
        include 'db.php';
        
        if (!$conn) {
            echo "<div class='error'>❌ Database connection failed</div>";
            exit;
        } else {
            echo "<div class='success'>✅ Database connected successfully</div>";
            echo "<p>Database: " . $conn->get_server_info() . "</p>";
        }
        
        echo "<h2>2. Required Tables Check</h2>";
        
        $required_tables = ['game_rooms', 'game_players', 'game_rounds', 'game_results'];
        $missing_tables = [];
        
        foreach ($required_tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "<div class='success'>✅ Table '$table' exists</div>";
                
                // Show table structure
                $structure = $conn->query("DESCRIBE $table");
                if ($structure) {
                    echo "<h4>Structure of $table:</h4>";
                    echo "<table>";
                    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
                    while ($row = $structure->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } else {
                echo "<div class='error'>❌ Table '$table' missing</div>";
                $missing_tables[] = $table;
            }
        }
        
        if (!empty($missing_tables)) {
            echo "<div class='error'>";
            echo "<h3>⚠️ Missing Tables Found</h3>";
            echo "<p>The following tables are missing: " . implode(', ', $missing_tables) . "</p>";
            echo "<p><strong>Solution:</strong> Run the database setup script:</p>";
            echo "<ul>";
            echo "<li><a href='quick_setup.php'>Quick Setup Tool</a></li>";
            echo "<li>Or manually run <code>complete_game_database.sql</code></li>";
            echo "</ul>";
            echo "</div>";
        }
        
        echo "<h2>3. Test Data Check</h2>";
        
        // Check for test room
        $test_room = 'TEST01';
        $room_check = $conn->query("SELECT * FROM game_rooms WHERE room_code = '$test_room'");
        
        if ($room_check && $room_check->num_rows > 0) {
            echo "<div class='success'>✅ Test room '$test_room' exists</div>";
            $room_data = $room_check->fetch_assoc();
            echo "<pre>" . print_r($room_data, true) . "</pre>";
        } else {
            echo "<div class='info'>ℹ️ Test room '$test_room' does not exist</div>";
            echo "<p>Creating test room...</p>";
            
            if (in_array('game_rooms', $missing_tables)) {
                echo "<div class='error'>❌ Cannot create test room - game_rooms table missing</div>";
            } else {
                $create_room = $conn->prepare("INSERT INTO game_rooms (room_code, room_name, host_name, status) VALUES (?, 'Test Room', 'TestUser', 'waiting')");
                $create_room->bind_param("s", $test_room);
                
                if ($create_room->execute()) {
                    echo "<div class='success'>✅ Test room created</div>";
                } else {
                    echo "<div class='error'>❌ Failed to create test room: " . $conn->error . "</div>";
                }
            }
        }
        
        echo "<h2>4. Game Handler Test</h2>";
        
        // Test the game handler directly
        echo "<p>Testing game handler response...</p>";
        
        $test_url = "http://localhost:3000/game_handler.php?action=test";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        $response = @file_get_contents($test_url, false, $context);
        
        if ($response === false) {
            echo "<div class='error'>❌ Failed to connect to game handler</div>";
            echo "<p>Possible issues:</p>";
            echo "<ul>";
            echo "<li>Web server not running</li>";
            echo "<li>PHP errors in game_handler.php</li>";
            echo "<li>Incorrect URL or port</li>";
            echo "</ul>";
        } else {
            echo "<div class='success'>✅ Game handler responded</div>";
            echo "<h4>Response:</h4>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            
            // Test JSON parsing
            $json_data = json_decode($response, true);
            if ($json_data === null) {
                echo "<div class='error'>❌ Response is not valid JSON</div>";
                echo "<p>JSON Error: " . json_last_error_msg() . "</p>";
            } else {
                echo "<div class='success'>✅ Response is valid JSON</div>";
                echo "<pre>" . print_r($json_data, true) . "</pre>";
            }
        }
        
        echo "<h2>5. Quick Actions</h2>";
        echo "<p>";
        echo "<a href='quick_setup.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🛠️ Run Quick Setup</a>";
        echo "<a href='test_response.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 Test Response Format</a>";
        echo "<a href='game_room.php?code=TEST01&name=TestUser' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🎮 Test Game Room</a>";
        echo "</p>";
        
        $conn->close();
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    }
    ?>
    
    <hr>
    <p style="text-align: center; color: #666;">
        <small>QuickMeet Database Check Tool</small>
    </p>
</body>
</html>
