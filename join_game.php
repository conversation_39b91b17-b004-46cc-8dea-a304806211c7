<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Join Game Room - QuickMeet</title>
  <meta name="description" content="Join a mathematical strategy game room in QuickMeet">
  <meta name="keywords" content="QuickMeet, math game, strategy game, multiplayer">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    .game-section {
      min-height: 100vh;
      padding: 120px 0 80px 0;
      position: relative;
    }

    .game-form {
      background: var(--surface-color);
      border-radius: 20px;
      padding: 3rem;
      border: 1px solid rgba(206, 175, 127, 0.1);
      max-width: 600px;
      margin: 0 auto;
    }

    .form-title {
      color: var(--heading-color);
      font-size: 2.5rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 2rem;
      font-family: var(--heading-font);
    }

    .game-info {
      background: rgba(206, 175, 127, 0.1);
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border-left: 4px solid var(--accent-color);
      text-align: center;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-label {
      color: var(--heading-color);
      font-weight: 600;
      margin-bottom: 0.5rem;
      display: block;
    }

    .form-control {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 12px;
      padding: 1rem 1.5rem;
      color: var(--default-color);
      font-size: 1rem;
      width: 100%;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--accent-color);
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 0 3px rgba(206, 175, 127, 0.2);
    }

    .form-control::placeholder {
      color: rgba(241, 243, 245, 0.6);
    }

    .btn-join {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 1rem 2rem;
      border-radius: 12px;
      font-weight: 600;
      font-size: 1.1rem;
      border: none;
      cursor: pointer;
      width: 100%;
      transition: all 0.3s ease;
    }

    .btn-join:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(206, 175, 127, 0.3);
    }

    .btn-back {
      background: transparent;
      border: 2px solid var(--accent-color);
      color: var(--accent-color);
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      text-decoration: none;
      font-weight: 600;
      display: inline-block;
      margin-bottom: 2rem;
      transition: all 0.3s ease;
    }

    .btn-back:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .btn-create {
      background: transparent;
      border: 2px solid var(--accent-color);
      color: var(--accent-color);
      padding: 1rem 2rem;
      border-radius: 12px;
      text-decoration: none;
      font-weight: 600;
      display: inline-block;
      width: 100%;
      text-align: center;
      margin-top: 1rem;
      transition: all 0.3s ease;
    }

    .btn-create:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .divider {
      text-align: center;
      margin: 2rem 0;
      position: relative;
    }

    .divider::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: rgba(206, 175, 127, 0.2);
    }

    .divider span {
      background: var(--surface-color);
      color: var(--default-color);
      padding: 0 1rem;
      position: relative;
    }
  </style>
</head>

<body class="index-page">

  <!-- Header -->
  <header id="header" class="header d-flex align-items-center sticky-top">
    <div class="container position-relative d-flex align-items-center justify-content-between">

      <a href="index.php" class="logo d-flex align-items-center me-auto me-xl-0">
        <img src="assets/img/bg/chat.png" alt="QuickMeet Logo" style="height: 40px; margin-right: 0.5rem;">
        <h1 class="sitename">QuickMeet</h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.php">Home</a></li>
          <li><a href="index.php#features">Features</a></li>
          <li><a href="index.php#how-it-works">How It Works</a></li>
          <li><a href="https://quickf.free.nf/" target="_blank">Quick</a></li>
          <li><a href="admin.php">Admin</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

      <div class="header-actions">
        <a class="cta-button" href="create_room.php">Create Chat Room</a>
      </div>

    </div>
  </header>

  <main class="main">

    <!-- Join Game Section -->
    <section class="game-section">
      <div class="container" data-aos="fade-up">
        
        <a href="index.php" class="btn-back">
          <i class="bi bi-arrow-left me-2"></i>Back to Home
        </a>

        <div class="game-form">
          <h1 class="form-title">🎯 Join Math Game</h1>
          
          <!-- Game Info -->
          <div class="game-info">
            <h4 style="color: var(--accent-color); margin-bottom: 1rem;">
              <i class="bi bi-info-circle me-2"></i>Quick Game Info
            </h4>
            <p style="color: var(--default-color); margin: 0;">
              🎮 2-5 player strategy game<br>
              🎯 Choose numbers 0-100 each round<br>
              🏆 Get closest to (average × 0.8) to win<br>
              ⏰ 1 minute per round • Start anytime!
            </p>
          </div>

          <!-- Join Form -->
          <form id="joinGameForm" method="POST" action="game_handler.php">
            <input type="hidden" name="action" value="join_game">
            
            <div class="form-group">
              <label class="form-label">Game Room Code</label>
              <input type="text" name="room_code" class="form-control" placeholder="Enter 6-digit room code" required maxlength="6" style="text-transform: uppercase;">
            </div>

            <div class="form-group">
              <label class="form-label">Your Name</label>
              <input type="text" name="player_name" class="form-control" placeholder="Enter your name" required maxlength="50">
            </div>

            <button type="submit" class="btn-join">
              <i class="bi bi-door-open me-2"></i>Join Game Room
            </button>
          </form>

          <div class="divider">
            <span>or</span>
          </div>

          <a href="create_game.php" class="btn-create">
            <i class="bi bi-plus-circle me-2"></i>Create New Game Room
          </a>
        </div>

      </div>
    </section>

  </main>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <script>
    // Initialize AOS
    AOS.init();

    // Auto-uppercase room code
    document.querySelector('input[name="room_code"]').addEventListener('input', function(e) {
      e.target.value = e.target.value.toUpperCase();
    });

    // Form submission
    document.getElementById('joinGameForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const formData = new FormData(this);
      const roomCode = formData.get('room_code');
      const playerName = formData.get('player_name');
      
      // Validate room code format
      if (!/^[A-Z0-9]{6}$/.test(roomCode)) {
        alert('Room code must be 6 characters (letters and numbers only)');
        return;
      }
      
      fetch('game_handler.php', {
        method: 'POST',
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Store player ID for this room
          localStorage.setItem(`player_id_${data.room_code}`, data.player_id);
          window.location.href = `game_room.php?code=${data.room_code}&name=${encodeURIComponent(data.player_name)}`;
        } else {
          alert('Error: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred. Please try again.');
      });
    });

    // Check for room code in URL (for direct links)
    const urlParams = new URLSearchParams(window.location.search);
    const roomCode = urlParams.get('code');
    if (roomCode) {
      document.querySelector('input[name="room_code"]').value = roomCode.toUpperCase();
    }
  </script>

</body>
</html>
