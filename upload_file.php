<?php
include 'db.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

$room_code = $_POST['room_code'] ?? '';
$sender_name = $_POST['sender_name'] ?? '';
$message_text = $_POST['message'] ?? '';

if (empty($room_code) || empty($sender_name)) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

// Check if file was uploaded
if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
    exit;
}

$file = $_FILES['file'];
$file_name = $file['name'];
$file_tmp = $file['tmp_name'];
$file_size = $file['size'];
$file_type = $file['type'];

// File size limit (10MB)
$max_size = 10 * 1024 * 1024;
if ($file_size > $max_size) {
    echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 10MB']);
    exit;
}

// Get file extension
$file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

// Allowed file types
$allowed_images = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
$allowed_documents = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'xls', 'xlsx', 'ppt', 'pptx'];
$allowed_other = ['zip', 'rar', '7z', 'mp3', 'mp4', 'avi', 'mov'];

$all_allowed = array_merge($allowed_images, $allowed_documents, $allowed_other);

if (!in_array($file_extension, $all_allowed)) {
    echo json_encode(['success' => false, 'message' => 'File type not allowed']);
    exit;
}

// Determine upload directory
$upload_dir = 'uploads/';
if (in_array($file_extension, $allowed_images)) {
    $upload_dir .= 'images/';
} elseif (in_array($file_extension, $allowed_documents)) {
    $upload_dir .= 'documents/';
} else {
    $upload_dir .= 'other/';
}

// Create directory if it doesn't exist
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// Generate unique filename
$unique_name = uniqid() . '_' . time() . '.' . $file_extension;
$file_path = $upload_dir . $unique_name;

// Move uploaded file
if (!move_uploaded_file($file_tmp, $file_path)) {
    echo json_encode(['success' => false, 'message' => 'Failed to save file']);
    exit;
}

// Insert into database
try {
    $stmt = $conn->prepare("INSERT INTO messages (room_code, sender_name, message, file_path, file_name, file_type, file_size, timestamp) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
    $stmt->bind_param("ssssssi", $room_code, $sender_name, $message_text, $file_path, $file_name, $file_type, $file_size);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true, 
            'message' => 'File uploaded successfully',
            'file_path' => $file_path,
            'file_name' => $file_name,
            'file_type' => $file_type
        ]);
    } else {
        // Delete uploaded file if database insert fails
        unlink($file_path);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
} catch (Exception $e) {
    // Delete uploaded file if database insert fails
    if (file_exists($file_path)) {
        unlink($file_path);
    }
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
