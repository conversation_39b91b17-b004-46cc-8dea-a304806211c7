<?php
include 'db.php';

// Admin password protection
session_start();
$admin_password = "admin2550";

// Check if user is trying to login
if (isset($_POST['admin_login'])) {
    if ($_POST['password'] === $admin_password) {
        $_SESSION['admin_logged_in'] = true;
        header("Location: admin.php");
        exit;
    } else {
        $login_error = "Invalid password. Please try again.";
    }
}

// Check if user is trying to logout
if (isset($_GET['logout'])) {
    session_destroy();
    header("Location: admin.php");
    exit;
}

// If not logged in, show login form
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8">
      <meta content="width=device-width, initial-scale=1.0" name="viewport">
      <title>Admin Login - QuickMeet</title>
      <meta name="description" content="Admin login for QuickMeet dashboard">

      <!-- Favicons -->
      <link href="assets/img/favicon.png" rel="icon">
      <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

      <!-- Fonts -->
      <link href="https://fonts.googleapis.com" rel="preconnect">
      <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
      <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

      <!-- Vendor CSS Files -->
      <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
      <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
      <link href="assets/vendor/aos/aos.css" rel="stylesheet">

      <!-- Main CSS File -->
      <link href="assets/css/main.css" rel="stylesheet">

      <style>
        .login-container {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 120px 0 80px 0;
        }

        .login-card {
          background: var(--surface-color);
          border-radius: 20px;
          padding: 3rem;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(206, 175, 127, 0.1);
          max-width: 400px;
          width: 100%;
          text-align: center;
        }

        .login-header {
          margin-bottom: 2rem;
        }

        .login-header h2 {
          color: var(--heading-color);
          margin-bottom: 0.5rem;
        }

        .login-header p {
          color: var(--default-color);
          opacity: 0.8;
        }

        .admin-icon {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, var(--accent-color), #d4b896);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1.5rem;
          font-size: 2rem;
          color: var(--contrast-color);
        }

        .form-group {
          margin-bottom: 1.5rem;
          text-align: left;
        }

        .form-group label {
          display: block;
          margin-bottom: 0.5rem;
          color: var(--heading-color);
          font-weight: 500;
        }

        .form-control-login {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(206, 175, 127, 0.2);
          border-radius: 10px;
          padding: 12px 16px;
          color: var(--default-color);
          font-size: 1rem;
          width: 100%;
          transition: all 0.3s ease;
        }

        .form-control-login:focus {
          background: rgba(255, 255, 255, 0.1);
          border-color: var(--accent-color);
          box-shadow: 0 0 0 0.2rem rgba(206, 175, 127, 0.25);
          color: var(--default-color);
          outline: none;
        }

        .form-control-login::placeholder {
          color: rgba(241, 243, 245, 0.6);
        }

        .btn-login {
          background: linear-gradient(135deg, var(--accent-color), #d4b896);
          color: var(--contrast-color);
          padding: 15px 40px;
          border-radius: 50px;
          border: none;
          font-weight: 600;
          font-size: 1.1rem;
          width: 100%;
          transition: all 0.3s ease;
          margin-top: 1rem;
        }

        .btn-login:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 30px rgba(206, 175, 127, 0.3);
          color: var(--contrast-color);
        }

        .error-alert {
          background: rgba(220, 53, 69, 0.1);
          border: 1px solid rgba(220, 53, 69, 0.3);
          color: #ff6b7a;
          border-radius: 10px;
          padding: 1rem;
          margin-bottom: 1.5rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .back-link {
          color: var(--accent-color);
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          margin-bottom: 2rem;
          transition: color 0.3s ease;
        }

        .back-link:hover {
          color: #d4b896;
        }

        .back-link i {
          margin-right: 0.5rem;
        }
      </style>
    </head>

    <body>
      <!-- Header -->
      <header id="header" class="header d-flex align-items-center sticky-top">
        <div class="container position-relative d-flex align-items-center justify-content-between">
          <a href="index.php" class="logo d-flex align-items-center me-auto me-xl-0">
            <img src="assets/img/chatbot-logo.png" alt="QuickMeet Logo" style="height: 40px; margin-right: 0.5rem;">
            <h1 class="sitename">QuickMeet</h1>
          </a>

          <nav id="navmenu" class="navmenu">
            <ul>
              <li><a href="index.php">Home</a></li>
              <li><a href="index.php#features">Features</a></li>
              <li><a href="index.php#how-it-works">How It Works</a></li>
              <li><a href="https://quickf.free.nf/" target="_blank">Quick</a></li>
              <li><a href="admin.php" class="active">Admin</a></li>
              <li><a href="create_room.php">Create Room</a></li>
              <li><a href="join_room.php">Join Room</a></li>
            </ul>
            <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
          </nav>
        </div>
      </header>

      <main class="main">
        <section class="login-container">
          <div class="container" data-aos="fade-up">
            <div class="row justify-content-center">
              <div class="col-lg-6">
                <a href="index.php" class="back-link">
                  <i class="bi bi-arrow-left"></i>Back to Home
                </a>

                <div class="login-card">
                  <div class="login-header">
                    <div class="admin-icon">
                      <i class="bi bi-shield-lock"></i>
                    </div>
                    <h2>Admin Login</h2>
                    <p>Enter the admin password to access the dashboard</p>
                  </div>

                  <?php if (isset($login_error)): ?>
                    <div class="error-alert">
                      <i class="bi bi-exclamation-triangle"></i>
                      <?= htmlspecialchars($login_error) ?>
                    </div>
                  <?php endif; ?>

                  <form method="post">
                    <div class="form-group">
                      <label for="password"><i class="bi bi-key me-2"></i>Admin Password</label>
                      <input type="password" class="form-control-login" id="password" name="password"
                             placeholder="Enter admin password" required autocomplete="off">
                    </div>

                    <button type="submit" name="admin_login" class="btn-login">
                      <i class="bi bi-unlock me-2"></i>Access Dashboard
                    </button>
                  </form>

                  <div style="margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid rgba(206, 175, 127, 0.1);">
                    <small style="color: rgba(241, 243, 245, 0.6);">
                      <i class="bi bi-info-circle me-1"></i>
                      Authorized personnel only. All access attempts are logged.
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <!-- Vendor JS Files -->
      <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
      <script src="assets/vendor/aos/aos.js"></script>

      <!-- Main JS File -->
      <script src="assets/js/main.js"></script>

      <script>
        // Auto-focus password input
        document.getElementById('password').focus();

        // Initialize AOS
        AOS.init();
      </script>

    </body>
    </html>
    <?php
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Admin Dashboard - QuickMeet</title>
  <meta name="description" content="Admin dashboard for managing chat rooms and messages">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    .admin-container {
      min-height: 100vh;
      padding: 120px 0 40px 0;
    }

    .admin-header {
      background: var(--surface-color);
      border-radius: 15px;
      padding: 2rem;
      margin-bottom: 2rem;
      border: 1px solid rgba(206, 175, 127, 0.1);
      text-align: center;
    }

    .admin-header h1 {
      color: var(--heading-color);
      margin-bottom: 0.5rem;
    }

    .admin-header p {
      color: var(--default-color);
      opacity: 0.8;
      margin: 0;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: var(--surface-color);
      border-radius: 15px;
      padding: 1.5rem;
      border: 1px solid rgba(206, 175, 127, 0.1);
      text-align: center;
      transition: transform 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      font-size: 1.5rem;
      color: var(--contrast-color);
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: var(--accent-color);
      display: block;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: var(--default-color);
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .admin-section {
      background: var(--surface-color);
      border-radius: 15px;
      padding: 2rem;
      margin-bottom: 2rem;
      border: 1px solid rgba(206, 175, 127, 0.1);
    }

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1.5rem;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .section-title {
      color: var(--heading-color);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-danger-admin {
      background: linear-gradient(135deg, #dc3545, #c82333);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 25px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-danger-admin:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
    }

    .room-card {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.1);
      border-radius: 10px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      transition: all 0.3s ease;
    }

    .room-card:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(206, 175, 127, 0.2);
    }

    .room-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .room-code {
      background: var(--accent-color);
      color: var(--contrast-color);
      padding: 0.25rem 0.75rem;
      border-radius: 15px;
      font-weight: 600;
      font-size: 0.9rem;
      letter-spacing: 0.1em;
    }

    .room-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .room-info-item {
      color: var(--default-color);
      font-size: 0.9rem;
    }

    .room-info-label {
      color: var(--accent-color);
      font-weight: 500;
    }

    .room-actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .btn-action {
      padding: 8px 16px;
      border-radius: 20px;
      border: none;
      font-size: 0.85rem;
      font-weight: 500;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-inspect {
      background: rgba(206, 175, 127, 0.2);
      color: var(--accent-color);
      border: 1px solid var(--accent-color);
    }

    .btn-inspect:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .btn-delete {
      background: rgba(220, 53, 69, 0.2);
      color: #ff6b7a;
      border: 1px solid #dc3545;
    }

    .btn-delete:hover {
      background: #dc3545;
      color: white;
    }

    .chat-messages {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid rgba(206, 175, 127, 0.1);
      border-radius: 10px;
      padding: 1rem;
      background: rgba(0, 0, 0, 0.1);
    }

    .chat-message {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 0.75rem;
      margin-bottom: 0.75rem;
      border-left: 3px solid var(--accent-color);
    }

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .message-sender {
      color: var(--accent-color);
      font-weight: 600;
      font-size: 0.9rem;
    }

    .message-time {
      color: rgba(241, 243, 245, 0.6);
      font-size: 0.8rem;
    }

    .message-content {
      color: var(--default-color);
      line-height: 1.4;
    }

    .query-section {
      margin-top: 2rem;
    }

    .query-form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .form-select-admin, .form-control-admin {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 10px;
      padding: 12px 16px;
      color: var(--default-color);
      font-size: 1rem;
    }

    .form-select-admin:focus, .form-control-admin:focus {
      background: rgba(255, 255, 255, 0.1);
      border-color: var(--accent-color);
      box-shadow: 0 0 0 0.2rem rgba(206, 175, 127, 0.25);
      color: var(--default-color);
      outline: none;
    }

    .form-select-admin option {
      background: var(--surface-color);
      color: var(--default-color);
      padding: 10px;
    }

    .form-select-admin optgroup {
      background: var(--surface-color);
      color: var(--accent-color);
      font-weight: 600;
    }

    .form-select-admin option:hover {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .form-select-admin option:checked {
      background: var(--accent-color);
      color: var(--contrast-color);
    }

    .form-control-admin {
      resize: vertical;
      min-height: 100px;
      font-family: 'Courier New', monospace;
    }

    .btn-run-query {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      border: none;
      padding: 12px 30px;
      border-radius: 25px;
      font-weight: 600;
      transition: all 0.3s ease;
      align-self: flex-start;
    }

    .btn-run-query:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(206, 175, 127, 0.3);
    }

    /* Messages Monitor Styles */
    .message-item-admin {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      transition: all 0.3s ease;
    }

    .message-item-admin:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: var(--accent-color);
    }

    .message-header-admin {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .message-info-admin {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .sender-name-admin {
      color: var(--accent-color);
      font-weight: 700;
      font-size: 1.1rem;
    }

    .room-code-admin {
      color: #17a2b8;
      font-size: 0.9rem;
      font-weight: 600;
    }

    .room-host-admin {
      color: rgba(241, 243, 245, 0.7);
      font-size: 0.85rem;
    }

    .message-time-admin {
      color: rgba(241, 243, 245, 0.6);
      font-size: 0.9rem;
      white-space: nowrap;
    }

    .message-content-admin {
      color: var(--default-color);
      font-size: 1rem;
      line-height: 1.5;
      margin-bottom: 1rem;
      padding: 0.75rem;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 8px;
      border-left: 3px solid var(--accent-color);
    }

    .file-attachment-admin {
      background: rgba(206, 175, 127, 0.1);
      border: 1px solid rgba(206, 175, 127, 0.3);
      border-radius: 10px;
      padding: 1rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-top: 0.5rem;
    }

    .file-icon-admin {
      font-size: 2rem;
      color: var(--accent-color);
      min-width: 40px;
    }

    .file-details-admin {
      flex: 1;
    }

    .file-name-admin {
      color: var(--default-color);
      font-weight: 600;
      font-size: 1rem;
      margin-bottom: 0.25rem;
    }

    .file-size-admin {
      color: rgba(241, 243, 245, 0.7);
      font-size: 0.85rem;
    }

    .file-actions-admin {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .btn-download-admin, .btn-preview-admin {
      background: var(--accent-color);
      color: var(--contrast-color);
      border: none;
      border-radius: 6px;
      padding: 0.5rem 1rem;
      font-size: 0.85rem;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-download-admin:hover, .btn-preview-admin:hover {
      background: #d4b896;
      transform: translateY(-1px);
      color: var(--contrast-color);
      text-decoration: none;
    }

    .btn-preview-admin {
      background: #17a2b8;
    }

    .btn-preview-admin:hover {
      background: #138496;
    }

    /* Room Cards Styles */
    .rooms-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 1.5rem;
      margin-top: 1rem;
    }

    .room-card-admin {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(206, 175, 127, 0.2);
      border-radius: 15px;
      padding: 1.5rem;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .room-card-admin:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: var(--accent-color);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(206, 175, 127, 0.2);
    }

    .room-header-admin {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid rgba(206, 175, 127, 0.1);
    }

    .room-header-admin h4 {
      color: var(--accent-color);
      font-weight: 700;
      font-size: 1.3rem;
      margin: 0;
    }

    .room-status-admin {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .room-status-admin.empty {
      background: linear-gradient(135deg, #6c757d, #5a6268);
    }

    .room-info-admin {
      margin-bottom: 1.5rem;
    }

    .room-info-item-admin {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .room-info-item-admin:last-child {
      border-bottom: none;
    }

    .room-info-label-admin {
      color: rgba(241, 243, 245, 0.7);
      font-size: 0.9rem;
      font-weight: 500;
    }

    .room-actions-admin {
      display: flex;
      gap: 0.75rem;
      justify-content: center;
    }

    .btn-inspect-admin, .btn-delete-admin {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
    }

    .btn-inspect-admin {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
    }

    .btn-inspect-admin:hover {
      background: linear-gradient(135deg, #d4b896, var(--accent-color));
      transform: translateY(-1px);
      box-shadow: 0 4px 15px rgba(206, 175, 127, 0.3);
    }

    .btn-delete-admin {
      background: linear-gradient(135deg, #dc3545, #c82333);
      color: white;
    }

    .btn-delete-admin:hover {
      background: linear-gradient(135deg, #c82333, #dc3545);
      transform: translateY(-1px);
      box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    /* Room Inspection Modal */
    .room-modal {
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(5px);
    }

    .room-modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--surface-color);
      border: 1px solid rgba(206, 175, 127, 0.3);
      border-radius: 20px;
      width: 90%;
      max-width: 1000px;
      max-height: 90vh;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    }

    .room-modal-header {
      background: linear-gradient(135deg, var(--accent-color), #d4b896);
      color: var(--contrast-color);
      padding: 1.5rem 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .room-modal-header h3 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
    }

    .room-modal-close {
      background: none;
      border: none;
      color: var(--contrast-color);
      font-size: 2rem;
      cursor: pointer;
      padding: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }

    .room-modal-close:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: rotate(90deg);
    }

    .room-modal-body {
      padding: 2rem;
      max-height: 70vh;
      overflow-y: auto;
    }

    .results-table {
      margin-top: 1.5rem;
      overflow-x: auto;
    }

    .table-admin {
      width: 100%;
      border-collapse: collapse;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 10px;
      overflow: hidden;
    }

    .table-admin th {
      background: var(--accent-color);
      color: var(--contrast-color);
      padding: 12px;
      font-weight: 600;
      text-align: left;
    }

    .table-admin td {
      padding: 12px;
      border-bottom: 1px solid rgba(206, 175, 127, 0.1);
      color: var(--default-color);
    }

    .table-admin tr:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .back-link {
      color: var(--accent-color);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      margin-bottom: 2rem;
      transition: color 0.3s ease;
    }

    .back-link:hover {
      color: #d4b896;
    }

    .back-link i {
      margin-right: 0.5rem;
    }

    .alert-success {
      background: rgba(40, 167, 69, 0.1);
      border: 1px solid rgba(40, 167, 69, 0.3);
      color: #6bcf7f;
      border-radius: 10px;
      padding: 1rem;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .no-data {
      text-align: center;
      color: rgba(241, 243, 245, 0.6);
      padding: 2rem;
      font-style: italic;
    }
  </style>
</head>

<body>

  <!-- Header -->
  <header id="header" class="header d-flex align-items-center sticky-top">
    <div class="container position-relative d-flex align-items-center justify-content-between">

      <a href="index.php" class="logo d-flex align-items-center me-auto me-xl-0">
        <img src="assets/img/chatbot-logo.png" alt="QuickMeet Logo" style="height: 40px; margin-right: 0.5rem;">
        <h1 class="sitename">QuickMeet</h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.php">Home</a></li>
          <li><a href="index.php#features">Features</a></li>
          <li><a href="index.php#how-it-works">How It Works</a></li>
          <li><a href="https://quickf.free.nf/" target="_blank">Quick</a></li>
          <li><a href="admin.php" class="active">Admin</a></li>
          <li><a href="create_room.php">Create Room</a></li>
          <li><a href="join_room.php">Join Room</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

    </div>
  </header>

  <main class="main">
    <section class="admin-container">
      <div class="container" data-aos="fade-up">
        <a href="index.php" class="back-link">
          <i class="bi bi-arrow-left"></i>Back to Home
        </a>

        <!-- Admin Header -->
        <div class="admin-header">
          <div class="d-flex justify-content-between align-items-center flex-wrap">
            <div>
              <h1><i class="bi bi-shield-check me-2"></i>Admin Dashboard</h1>
              <p>Manage chat rooms, monitor activity, and analyze usage statistics</p>
            </div>
            <div>
              <a href="admin.php?logout=1" class="btn btn-outline-danger"
                 onclick="return confirm('Are you sure you want to logout?')">
                <i class="bi bi-box-arrow-right me-2"></i>Logout
              </a>
            </div>
          </div>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
          <?php
          // Get statistics
          $total_rooms = $conn->query("SELECT COUNT(*) as count FROM rooms")->fetch_assoc()['count'];
          $total_messages = $conn->query("SELECT COUNT(*) as count FROM messages")->fetch_assoc()['count'];
          $active_today = $conn->query("SELECT COUNT(*) as count FROM rooms WHERE DATE(created_at) = CURDATE()")->fetch_assoc()['count'];
          $messages_today = $conn->query("SELECT COUNT(*) as count FROM messages WHERE DATE(timestamp) = CURDATE()")->fetch_assoc()['count'];
          ?>

          <div class="stat-card" data-aos="fade-up" data-aos-delay="100">
            <div class="stat-icon">
              <i class="bi bi-house-door"></i>
            </div>
            <span class="stat-number"><?= $total_rooms ?></span>
            <div class="stat-label">Total Rooms</div>
          </div>

          <div class="stat-card" data-aos="fade-up" data-aos-delay="200">
            <div class="stat-icon">
              <i class="bi bi-chat-dots"></i>
            </div>
            <span class="stat-number"><?= $total_messages ?></span>
            <div class="stat-label">Total Messages</div>
          </div>

          <div class="stat-card" data-aos="fade-up" data-aos-delay="300">
            <div class="stat-icon">
              <i class="bi bi-calendar-day"></i>
            </div>
            <span class="stat-number"><?= $active_today ?></span>
            <div class="stat-label">Rooms Today</div>
          </div>

          <div class="stat-card" data-aos="fade-up" data-aos-delay="400">
            <div class="stat-icon">
              <i class="bi bi-chat-square-text"></i>
            </div>
            <span class="stat-number"><?= $messages_today ?></span>
            <div class="stat-label">Messages Today</div>
          </div>

          <?php
          // Get file statistics
          $total_files = $conn->query("SELECT COUNT(*) as count FROM messages WHERE file_path IS NOT NULL")->fetch_assoc()['count'];
          ?>
          <div class="stat-card" data-aos="fade-up" data-aos-delay="500">
            <div class="stat-icon">
              <i class="bi bi-file-earmark"></i>
            </div>
            <span class="stat-number"><?= $total_files ?></span>
            <div class="stat-label">Files Shared</div>
          </div>
        </div>
        <?php
        // Handle delete all action
        if (isset($_POST['delete_all'])) {
            $conn->query("DELETE FROM messages");
            $conn->query("DELETE FROM rooms");
            echo '<div class="alert-success">';
            echo '<i class="bi bi-check-circle"></i>';
            echo 'All data deleted successfully.';
            echo '</div>';
        }

        // Handle individual room deletion
        if (isset($_GET['delete'])) {
            $code = $_GET['delete'];
            $conn->query("DELETE FROM messages WHERE room_code = '$code'");
            $conn->query("DELETE FROM rooms WHERE room_code = '$code'");
            echo '<div class="alert-success">';
            echo '<i class="bi bi-check-circle"></i>';
            echo "Room $code deleted successfully.";
            echo '</div>';
        }
        ?>

        <!-- Messages & Files Monitor -->
        <div class="admin-section" data-aos="fade-up" data-aos-delay="450">
          <div class="section-header">
            <h3 class="section-title">
              <i class="bi bi-chat-dots"></i>Messages & Files Monitor
            </h3>
            <p>View all chat messages and shared files across all rooms</p>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <select id="messageFilter" class="form-select-admin" onchange="filterMessages()">
                <option value="all">All Messages</option>
                <option value="text">Text Only</option>
                <option value="files">Files Only</option>
                <option value="images">Images Only</option>
                <option value="documents">Documents Only</option>
              </select>
            </div>
            <div class="col-md-6">
              <input type="text" id="searchMessages" class="form-control-admin"
                     placeholder="Search messages, sender names, or room codes..."
                     onkeyup="searchMessages()">
            </div>
          </div>

          <div class="messages-container" style="max-height: 600px; overflow-y: auto; border: 1px solid rgba(206, 175, 127, 0.2); border-radius: 10px; padding: 1rem;">
            <?php
            // Get all messages with room info
            $messages_query = "
              SELECT m.*, r.name as room_host, r.phone as host_phone
              FROM messages m
              LEFT JOIN rooms r ON m.room_code = r.room_code
              ORDER BY m.timestamp DESC
              LIMIT 50
            ";
            $messages_result = $conn->query($messages_query);

            if ($messages_result->num_rows > 0) {
              while ($msg = $messages_result->fetch_assoc()) {
                $timestamp = new DateTime($msg['timestamp']);
                $formatted_time = $timestamp->format('M j, Y H:i');

                $message_type = 'text';
                if (!empty($msg['file_path'])) {
                  if (strpos($msg['file_type'], 'image') !== false) {
                    $message_type = 'image';
                  } else {
                    $message_type = 'file';
                  }
                }

                echo '<div class="message-item-admin" data-type="' . $message_type . '">';
                echo '<div class="message-header-admin">';
                echo '<div class="message-info-admin">';
                echo '<span class="sender-name-admin">' . htmlspecialchars($msg['sender_name']) . '</span>';
                echo '<span class="room-code-admin">Room: ' . htmlspecialchars($msg['room_code']) . '</span>';
                if ($msg['room_host']) {
                  echo '<span class="room-host-admin">Host: ' . htmlspecialchars($msg['room_host']) . '</span>';
                }
                echo '</div>';
                echo '<span class="message-time-admin">' . $formatted_time . '</span>';
                echo '</div>';

                // Display message content
                if (!empty($msg['message'])) {
                  echo '<div class="message-content-admin">' . htmlspecialchars($msg['message']) . '</div>';
                }

                // Display file if exists
                if (!empty($msg['file_path'])) {
                  $file_size_formatted = '';
                  if ($msg['file_size']) {
                    if ($msg['file_size'] < 1024) {
                      $file_size_formatted = $msg['file_size'] . ' B';
                    } elseif ($msg['file_size'] < 1024 * 1024) {
                      $file_size_formatted = round($msg['file_size'] / 1024, 1) . ' KB';
                    } else {
                      $file_size_formatted = round($msg['file_size'] / (1024 * 1024), 1) . ' MB';
                    }
                  }

                  // Determine file icon
                  $file_icon = 'bi-file-earmark';
                  if (strpos($msg['file_type'], 'image') !== false) {
                    $file_icon = 'bi-image';
                  } elseif (strpos($msg['file_type'], 'pdf') !== false) {
                    $file_icon = 'bi-file-earmark-pdf';
                  } elseif (strpos($msg['file_type'], 'word') !== false || strpos($msg['file_type'], 'document') !== false) {
                    $file_icon = 'bi-file-earmark-word';
                  } elseif (strpos($msg['file_type'], 'excel') !== false || strpos($msg['file_type'], 'spreadsheet') !== false) {
                    $file_icon = 'bi-file-earmark-excel';
                  }

                  echo '<div class="file-attachment-admin">';
                  echo '<i class="bi ' . $file_icon . ' file-icon-admin"></i>';
                  echo '<div class="file-details-admin">';
                  echo '<div class="file-name-admin">' . htmlspecialchars($msg['file_name']) . '</div>';
                  echo '<div class="file-size-admin">' . $file_size_formatted . ' • ' . htmlspecialchars($msg['file_type']) . '</div>';
                  echo '</div>';
                  echo '<div class="file-actions-admin">';
                  echo '<a href="' . htmlspecialchars($msg['file_path']) . '" download="' . htmlspecialchars($msg['file_name']) . '" class="btn-download-admin">';
                  echo '<i class="bi bi-download"></i> Download';
                  echo '</a>';
                  if (strpos($msg['file_type'], 'image') !== false) {
                    echo '<button class="btn-preview-admin" onclick="previewImage(\'' . htmlspecialchars($msg['file_path']) . '\', \'' . htmlspecialchars($msg['file_name']) . '\')">';
                    echo '<i class="bi bi-eye"></i> Preview';
                    echo '</button>';
                  }
                  echo '</div>';
                  echo '</div>';
                }

                echo '</div>';
              }
            } else {
              echo '<div class="no-data">No messages found.</div>';
            }
            ?>
          </div>
        </div>

        <!-- Danger Zone -->
        <div class="admin-section" data-aos="fade-up" data-aos-delay="500">
          <div class="section-header">
            <h3 class="section-title">
              <i class="bi bi-exclamation-triangle"></i>Danger Zone
            </h3>
            <form method="post" style="margin: 0;">
              <button type="submit" name="delete_all" class="btn-danger-admin"
                      onclick="return confirm('⚠️ This will permanently delete ALL rooms and messages. Are you absolutely sure?')">
                <i class="bi bi-trash3 me-2"></i>Delete All Data
              </button>
            </form>
          </div>
          <p style="color: #ff6b7a; font-weight: 500; margin: 0;">Permanently delete all chat rooms, messages, and associated data. This action cannot be undone.</p>
        </div>

        <!-- Chat Rooms Management -->
        <div class="admin-section" data-aos="fade-up" data-aos-delay="600">
          <div class="section-header">
            <h3 class="section-title">
              <i class="bi bi-house-door"></i>Chat Rooms (<?= $total_rooms ?>)
            </h3>
            <p>Click on any room to inspect messages and download files</p>
          </div>

          <div class="rooms-grid">
            <?php
            $result = $conn->query("SELECT * FROM rooms ORDER BY created_at DESC");
            if ($result->num_rows > 0) {
                while ($room = $result->fetch_assoc()) {
                    $message_count = $conn->query("SELECT COUNT(*) as count FROM messages WHERE room_code = '{$room['room_code']}'")->fetch_assoc()['count'];
                    $file_count = $conn->query("SELECT COUNT(*) as count FROM messages WHERE room_code = '{$room['room_code']}' AND file_path IS NOT NULL")->fetch_assoc()['count'];
                    $last_activity = $conn->query("SELECT MAX(timestamp) as last_msg FROM messages WHERE room_code = '{$room['room_code']}'")->fetch_assoc()['last_msg'];

                    echo '<div class="room-card-admin" onclick="inspectRoom(\'' . htmlspecialchars($room['room_code']) . '\')" style="cursor: pointer;">';
                    echo '<div class="room-header-admin">';
                    echo '<h4>Room ' . htmlspecialchars($room['room_code']) . '</h4>';
                    echo '<span class="room-status-admin">' . ($message_count > 0 ? 'Active' : 'Empty') . '</span>';
                    echo '</div>';

                    echo '<div class="room-info-admin">';
                    echo '<div class="room-info-item-admin">';
                    echo '<span class="room-info-label-admin">Host:</span> ' . htmlspecialchars($room['name']);
                    echo '</div>';
                    echo '<div class="room-info-item-admin">';
                    echo '<span class="room-info-label-admin">Phone:</span> ' . htmlspecialchars($room['phone']);
                    echo '</div>';
                    echo '<div class="room-info-item-admin">';
                    echo '<span class="room-info-label-admin">Age:</span> ' . htmlspecialchars($room['age']) . ' • ' . htmlspecialchars($room['gender']);
                    echo '</div>';
                    echo '<div class="room-info-item-admin">';
                    echo '<span class="room-info-label-admin">Created:</span> ' . date('M j, Y H:i', strtotime($room['created_at']));
                    echo '</div>';
                    echo '<div class="room-info-item-admin">';
                    echo '<span class="room-info-label-admin">Messages:</span> ' . $message_count;
                    echo '</div>';
                    echo '<div class="room-info-item-admin">';
                    echo '<span class="room-info-label-admin">Files:</span> ' . $file_count;
                    echo '</div>';
                    if ($last_activity) {
                        echo '<div class="room-info-item-admin">';
                        echo '<span class="room-info-label-admin">Last Activity:</span> ' . date('M j, H:i', strtotime($last_activity));
                        echo '</div>';
                    }
                    echo '</div>';
                    echo '<div class="room-actions-admin" onclick="event.stopPropagation();">';
                    echo '<button type="button" class="btn-inspect-admin" onclick="inspectRoom(\'' . htmlspecialchars($room['room_code']) . '\')">';
                    echo '<i class="bi bi-eye"></i> Inspect';
                    echo '</button>';
                    echo '<form method="post" style="display: inline;" onsubmit="return confirm(\'Are you sure you want to delete this room and all its messages?\')">';
                    echo '<input type="hidden" name="delete_room" value="' . htmlspecialchars($room['room_code']) . '">';
                    echo '<button type="submit" class="btn-delete-admin">';
                    echo '<i class="bi bi-trash"></i> Delete';
                    echo '</button>';
                    echo '</form>';
                    echo '</div>';
                    echo '</div>';
              }
          } else {
              echo '<div class="no-data">';
              echo '<i class="bi bi-house-door" style="font-size: 3rem; color: var(--accent-color); margin-bottom: 1rem; display: block;"></i>';
              echo 'No chat rooms found. Rooms will appear here once users start creating them.';
              echo '</div>';
          }
          ?>
        </div>

        <!-- Room Inspection Modal -->
        <div id="roomInspectionModal" class="room-modal" style="display: none;">
          <div class="room-modal-content">
            <div class="room-modal-header">
              <h3 id="modalRoomTitle">Room Inspection</h3>
              <button class="room-modal-close" onclick="closeRoomModal()">&times;</button>
            </div>
            <div class="room-modal-body">
              <div id="roomInspectionContent">
                <div style="text-align: center; padding: 2rem;">
                  <i class="bi bi-hourglass-split" style="font-size: 3rem; color: var(--accent-color);"></i>
                  <p style="margin-top: 1rem; color: var(--default-color);">Loading room data...</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Room Inspection -->
        <?php if (isset($_GET['inspect'])): ?>
        <?php
        $code = $_GET['inspect'];
        $room_result = $conn->query("SELECT * FROM rooms WHERE room_code = '$code' LIMIT 1");
        $room_data = $room_result->fetch_assoc();
        ?>
        <div class="admin-section" data-aos="fade-up" data-aos-delay="700">
          <div class="section-header">
            <h3 class="section-title">
              <i class="bi bi-chat-square-dots"></i>Room <?= htmlspecialchars($code) ?> - Chat History
            </h3>
            <a href="admin.php" class="btn-action btn-inspect">
              <i class="bi bi-x-circle"></i>Close
            </a>
          </div>

          <?php if ($room_data): ?>
          <div class="room-info mb-3">
            <div class="room-info-item">
              <span class="room-info-label">Host:</span> <?= htmlspecialchars($room_data['name']) ?>
            </div>
            <div class="room-info-item">
              <span class="room-info-label">Created:</span> <?= date('M j, Y H:i', strtotime($room_data['created_at'])) ?>
            </div>
          </div>
          <?php endif; ?>

          <div class="chat-messages">
            <?php
            $messages = $conn->query("SELECT * FROM messages WHERE room_code = '$code' ORDER BY timestamp ASC");
            if ($messages->num_rows > 0) {
                while ($msg = $messages->fetch_assoc()) {
                    echo '<div class="chat-message">';
                    echo '<div class="message-header">';
                    echo '<span class="message-sender">' . htmlspecialchars($msg['sender_name']) . '</span>';
                    echo '<span class="message-time">' . date('M j, H:i', strtotime($msg['timestamp'])) . '</span>';
                    echo '</div>';
                    echo '<div class="message-content">' . htmlspecialchars($msg['message']) . '</div>';
                    echo '</div>';
                }
            } else {
                echo '<div class="no-data">';
                echo '<i class="bi bi-chat-dots" style="font-size: 2rem; color: var(--accent-color); margin-bottom: 1rem; display: block;"></i>';
                echo 'No messages in this room yet.';
                echo '</div>';
            }
            ?>
          </div>
        </div>
        <?php endif; ?>

        <!-- SQL Query Tool (Full Width) -->
        <div class="admin-section query-section" data-aos="fade-up" data-aos-delay="800" style="width: 100%; max-width: none;">
          <div class="section-header">
            <h3 class="section-title">
              <i class="bi bi-terminal"></i>SQL Query Tool
            </h3>
          </div>

          <form method="post" class="query-form">
            <!-- Predefined Queries Section (Upper) -->
            <div class="mb-4">
              <label for="query_select" class="form-label" style="color: var(--heading-color); margin-bottom: 1rem; display: block; font-size: 1.3rem; font-weight: 700;">
                <i class="bi bi-list-ul me-2"></i>Predefined Queries
              </label>
              <select id="query_select" class="form-select-admin" onchange="insertQuery()"
                      style="width: 100%; padding: 20px 25px; font-size: 1.1rem; min-height: 70px; max-width: none;">
                <option value="">-- Select a predefined query --</option>

                <optgroup label="📁 Room Analysis">
                  <option value="all_rooms">🗃️ All Rooms with Details</option>
                  <option value="room_count">🔢 Count of Total Rooms</option>
                  <option value="rooms_today">📅 Rooms Created Today</option>
                  <option value="room_per_day">📊 Rooms Created Per Day</option>
                  <option value="gender_count">👥 Room Count by Gender</option>
                  <option value="host_stats">🧑 Host Statistics</option>
                </optgroup>

                <optgroup label="💬 Chat Analysis">
                  <option value="all_messages">💬 All Messages</option>
                  <option value="top_rooms">🔥 Most Active Rooms</option>
                  <option value="messages_today">🕒 Messages Today</option>
                  <option value="messages_last_hour">🕐 Recent Messages</option>
                  <option value="messages_per_user">📊 User Activity</option>
                </optgroup>

                <optgroup label="🧹 Maintenance (Caution)">
                  <option value="delete_old_rooms">🗑️ Delete Old Rooms</option>
                  <option value="delete_empty_rooms">🗑️ Delete Empty Rooms</option>
                </optgroup>
              </select>
            </div>

            <!-- SQL Query Section (Lower) -->
            <div class="mb-4">
              <label for="query_area" class="form-label" style="color: var(--heading-color); margin-bottom: 1rem; display: block; font-size: 1.3rem; font-weight: 700;">
                <i class="bi bi-code-slash me-2"></i>SQL Query
              </label>
              <textarea name="query" id="query_area" class="form-control-admin" rows="15"
                        placeholder="Enter your SQL query here or select from predefined queries above..."
                        style="width: 100%; min-height: 400px; font-family: 'Courier New', monospace; font-size: 1.1rem; padding: 20px 25px; resize: vertical; max-width: none;"></textarea>
            </div>

            <div class="text-center mt-4">
              <button type="submit" class="btn-run-query" style="padding: 15px 40px; font-size: 1.1rem; min-width: 200px;">
                <i class="bi bi-play-circle me-2"></i>Execute Query
              </button>
            </div>
          </form>

          <?php
          if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['query'])) {
              $sql = trim($_POST['query']);
              if (!empty($sql)) {
                  try {
                      $res = $conn->query($sql);
                      if ($res) {
                          if ($res->num_rows > 0) {
                              echo '<div class="results-table">';
                              echo '<h5 style="color: var(--heading-color); margin-bottom: 1rem;"><i class="bi bi-table me-2"></i>Query Results (' . $res->num_rows . ' rows)</h5>';
                              echo '<table class="table-admin">';

                              // Get first row to create headers
                              $first_row = $res->fetch_assoc();
                              if ($first_row) {
                                  echo '<thead><tr>';
                                  foreach ($first_row as $col => $val) {
                                      echo '<th>' . htmlspecialchars($col) . '</th>';
                                  }
                                  echo '</tr></thead><tbody>';

                                  // Display first row
                                  echo '<tr>';
                                  foreach ($first_row as $val) {
                                      echo '<td>' . htmlspecialchars($val ?? 'NULL') . '</td>';
                                  }
                                  echo '</tr>';

                                  // Display remaining rows
                                  while ($row = $res->fetch_assoc()) {
                                      echo '<tr>';
                                      foreach ($row as $val) {
                                          echo '<td>' . htmlspecialchars($val ?? 'NULL') . '</td>';
                                      }
                                      echo '</tr>';
                                  }
                                  echo '</tbody>';
                              }
                              echo '</table>';
                              echo '</div>';
                          } else {
                              echo '<div class="alert-success">';
                              echo '<i class="bi bi-check-circle"></i>';
                              echo 'Query executed successfully. No results returned.';
                              echo '</div>';
                          }
                      } else {
                          echo '<div class="alert alert-danger" style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #ff6b7a; border-radius: 10px; padding: 1rem; margin-top: 1rem;">';
                          echo '<i class="bi bi-exclamation-triangle me-2"></i>';
                          echo 'Query Error: ' . htmlspecialchars($conn->error);
                          echo '</div>';
                      }
                  } catch (Exception $e) {
                      echo '<div class="alert alert-danger" style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #ff6b7a; border-radius: 10px; padding: 1rem; margin-top: 1rem;">';
                      echo '<i class="bi bi-exclamation-triangle me-2"></i>';
                      echo 'Error: ' . htmlspecialchars($e->getMessage());
                      echo '</div>';
                  }
              }
          }
          ?>
        </div>

      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer id="footer" class="footer dark-background">
    <div class="container">
      <div class="row gy-3">
        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-geo-alt icon"></i>
          <div class="address">
            <h4>Address</h4>
            <p>QuickMeet HQ<br>Digital Communication Center<br></p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-telephone icon"></i>
          <div>
            <h4>Contact</h4>
            <p>
              <strong>Phone:</strong> <span>+91 7028844513</span><br>
              <strong>Email:</strong> <span><EMAIL></span><br>
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6 d-flex">
          <i class="bi bi-clock icon"></i>
          <div>
            <h4>Available</h4>
            <p>
              <strong>24/7 Service</strong><br>
              Always online and ready to connect you with friends and family.
            </p>
          </div>
        </div>

        <div class="col-lg-3 col-md-6">
          <h4>Follow Us</h4>
          <div class="social-links d-flex">
            <a href="#" class="twitter"><i class="bi bi-twitter-x"></i></a>
            <a href="#" class="facebook"><i class="bi bi-facebook"></i></a>
            <a href="#" class="instagram"><i class="bi bi-instagram"></i></a>
            <a href="#" class="linkedin"><i class="bi bi-linkedin"></i></a>
          </div>
        </div>
      </div>
    </div>

    <div class="container copyright text-center mt-4">
      <p>© <span>Copyright</span> <strong class="px-1 sitename">QuickMeet</strong> <span>All Rights Reserved</span></p>
    </div>
  </footer>

  <!-- Scroll Top -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

  <script>
    // Predefined queries map
    const queryMap = {
      // Room Analysis
      'all_rooms': "SELECT room_code, name, phone, age, gender, created_at FROM rooms ORDER BY created_at DESC;",
      'room_count': "SELECT COUNT(*) AS total_rooms FROM rooms;",
      'rooms_today': "SELECT room_code, name, created_at FROM rooms WHERE DATE(created_at) = CURDATE();",
      'room_per_day': "SELECT DATE(created_at) AS day, COUNT(*) AS rooms_created FROM rooms GROUP BY DATE(created_at) ORDER BY day DESC LIMIT 10;",
      'gender_count': "SELECT gender, COUNT(*) AS total FROM rooms GROUP BY gender;",
      'host_stats': "SELECT name, COUNT(*) AS total_rooms FROM rooms GROUP BY name ORDER BY total_rooms DESC LIMIT 10;",

      // Chat Analysis
      'all_messages': "SELECT room_code, sender_name, message, timestamp FROM messages ORDER BY timestamp DESC LIMIT 50;",
      'top_rooms': "SELECT room_code, COUNT(*) AS message_count FROM messages GROUP BY room_code ORDER BY message_count DESC LIMIT 10;",
      'messages_today': "SELECT room_code, sender_name, message, timestamp FROM messages WHERE DATE(timestamp) = CURDATE() ORDER BY timestamp DESC;",
      'messages_last_hour': "SELECT room_code, sender_name, message, timestamp FROM messages WHERE timestamp >= NOW() - INTERVAL 1 HOUR ORDER BY timestamp DESC;",
      'messages_per_user': "SELECT sender_name, COUNT(*) AS total_messages FROM messages GROUP BY sender_name ORDER BY total_messages DESC LIMIT 10;",

      // Cleanup (Use with caution)
      'delete_old_rooms': "DELETE FROM rooms WHERE created_at < NOW() - INTERVAL 30 DAY;",
      'delete_empty_rooms': "DELETE FROM rooms WHERE room_code NOT IN (SELECT DISTINCT room_code FROM messages);"
    };

    // Insert selected query into textarea
    function insertQuery() {
      const selected = document.getElementById("query_select").value;
      const queryArea = document.getElementById("query_area");
      if (selected && queryMap[selected]) {
        queryArea.value = queryMap[selected];
        queryArea.focus();
      }
    }

    // Messages filtering and search functions
    function filterMessages() {
      const filter = document.getElementById('messageFilter').value;
      const messages = document.querySelectorAll('.message-item-admin');

      messages.forEach(message => {
        const type = message.getAttribute('data-type');
        let show = false;

        switch(filter) {
          case 'all':
            show = true;
            break;
          case 'text':
            show = type === 'text';
            break;
          case 'files':
            show = type === 'file' || type === 'image';
            break;
          case 'images':
            show = type === 'image';
            break;
          case 'documents':
            show = type === 'file';
            break;
        }

        message.style.display = show ? 'block' : 'none';
      });
    }

    function searchMessages() {
      const searchTerm = document.getElementById('searchMessages').value.toLowerCase();
      const messages = document.querySelectorAll('.message-item-admin');

      messages.forEach(message => {
        const senderName = message.querySelector('.sender-name-admin').textContent.toLowerCase();
        const roomCode = message.querySelector('.room-code-admin').textContent.toLowerCase();
        const messageContent = message.querySelector('.message-content-admin');
        const fileName = message.querySelector('.file-name-admin');

        let searchableText = senderName + ' ' + roomCode;
        if (messageContent) {
          searchableText += ' ' + messageContent.textContent.toLowerCase();
        }
        if (fileName) {
          searchableText += ' ' + fileName.textContent.toLowerCase();
        }

        const matches = searchableText.includes(searchTerm);
        message.style.display = matches ? 'block' : 'none';
      });
    }

    // Image preview modal
    function previewImage(imagePath, fileName) {
      // Create modal if it doesn't exist
      let modal = document.getElementById('imagePreviewModal');
      if (!modal) {
        modal = document.createElement('div');
        modal.id = 'imagePreviewModal';
        modal.style.cssText = `
          display: none;
          position: fixed;
          z-index: 1000;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.9);
          cursor: pointer;
        `;

        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          max-width: 90%;
          max-height: 90%;
          text-align: center;
        `;

        const modalImage = document.createElement('img');
        modalImage.id = 'modalPreviewImage';
        modalImage.style.cssText = `
          max-width: 100%;
          max-height: 80vh;
          border-radius: 8px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        `;

        const modalTitle = document.createElement('div');
        modalTitle.id = 'modalPreviewTitle';
        modalTitle.style.cssText = `
          color: white;
          font-size: 1.2rem;
          margin-top: 1rem;
          font-weight: 600;
        `;

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '✕';
        closeButton.style.cssText = `
          position: absolute;
          top: 20px;
          right: 30px;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border: none;
          font-size: 2rem;
          cursor: pointer;
          border-radius: 50%;
          width: 50px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
        `;

        closeButton.onclick = () => modal.style.display = 'none';
        modal.onclick = () => modal.style.display = 'none';

        modalContent.appendChild(modalImage);
        modalContent.appendChild(modalTitle);
        modal.appendChild(modalContent);
        modal.appendChild(closeButton);
        document.body.appendChild(modal);
      }

      // Show modal with image
      document.getElementById('modalPreviewImage').src = imagePath;
      document.getElementById('modalPreviewTitle').textContent = fileName;
      modal.style.display = 'block';
    }

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        const modal = document.getElementById('imagePreviewModal');
        if (modal) {
          modal.style.display = 'none';
        }
      }
    });

    // Room inspection functions
    function inspectRoom(roomCode) {
      const modal = document.getElementById('roomInspectionModal');
      const title = document.getElementById('modalRoomTitle');
      const content = document.getElementById('roomInspectionContent');

      title.textContent = `Room ${roomCode} - Inspection`;
      content.innerHTML = `
        <div style="text-align: center; padding: 2rem;">
          <i class="bi bi-hourglass-split" style="font-size: 3rem; color: var(--accent-color);"></i>
          <p style="margin-top: 1rem; color: var(--default-color);">Loading room data...</p>
        </div>
      `;

      modal.style.display = 'block';

      // Fetch room data
      fetch('get_room_data.php?room_code=' + encodeURIComponent(roomCode))
        .then(response => response.text())
        .then(data => {
          content.innerHTML = data;
        })
        .catch(error => {
          console.error('Error loading room data:', error);
          content.innerHTML = `
            <div style="text-align: center; padding: 2rem;">
              <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: #dc3545;"></i>
              <p style="margin-top: 1rem; color: #dc3545;">Error loading room data. Please try again.</p>
            </div>
          `;
        });
    }

    function closeRoomModal() {
      document.getElementById('roomInspectionModal').style.display = 'none';
    }

    // Close modal when clicking outside
    document.getElementById('roomInspectionModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeRoomModal();
      }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        const modal = document.getElementById('roomInspectionModal');
        if (modal && modal.style.display === 'block') {
          closeRoomModal();
        }
      }
    });

    // Initialize AOS
    AOS.init();

    // Auto-resize textarea
    const textarea = document.getElementById('query_area');
    if (textarea) {
      textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.max(100, this.scrollHeight) + 'px';
      });
    }
  </script>

</body>
</html>
