<?php
include 'db.php';

// Admin password protection
session_start();
$admin_password = "admin2550";

// Check if user is trying to login
if (isset($_POST['admin_login'])) {
    if ($_POST['password'] === $admin_password) {
        $_SESSION['admin_logged_in'] = true;
        header("Location: admin.php");
        exit;
    } else {
        $login_error = "Invalid password. Please try again.";
    }
}

// Check if user is trying to logout
if (isset($_GET['logout'])) {
    session_destroy();
    header("Location: admin_game.php");
    exit;
}

// If not logged in, show login form
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8">
      <meta content="width=device-width, initial-scale=1.0" name="viewport">
      <title>Game Admin - QuickMeet</title>
      <meta name="description" content="Game admin dashboard for QuickMeet mathematical strategy game">

      <!-- Vendor CSS Files -->
      <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
      <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
      <link href="assets/css/main.css" rel="stylesheet">

      <style>
        body {
          background: var(--background-color);
          color: var(--default-color);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .login-container {
          background: var(--surface-color);
          padding: 3rem;
          border-radius: 15px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
          max-width: 400px;
          width: 100%;
        }
        .login-header {
          text-align: center;
          margin-bottom: 2rem;
        }
        .login-header h1 {
          color: var(--accent-color);
          font-size: 2rem;
          margin-bottom: 0.5rem;
        }
        .form-control {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(206, 175, 127, 0.2);
          color: var(--default-color);
          padding: 0.75rem;
          border-radius: 8px;
        }
        .form-control:focus {
          background: rgba(255, 255, 255, 0.15);
          border-color: var(--accent-color);
          color: var(--default-color);
          box-shadow: 0 0 0 0.2rem rgba(206, 175, 127, 0.25);
        }
        .btn-login {
          background: var(--accent-color);
          color: var(--contrast-color);
          border: none;
          padding: 0.75rem 2rem;
          border-radius: 8px;
          font-weight: 600;
          width: 100%;
        }
        .btn-login:hover {
          background: #b8956f;
          color: var(--contrast-color);
        }
        .error-message {
          background: rgba(220, 53, 69, 0.1);
          color: #dc3545;
          padding: 0.75rem;
          border-radius: 8px;
          margin-bottom: 1rem;
          text-align: center;
        }
      </style>
    </head>

    <body>
      <div class="login-container">
        <div class="login-header">
          <h1><i class="bi bi-controller"></i> Game Admin</h1>
          <p>QuickMeet Mathematical Strategy Game</p>
        </div>

        <?php if (isset($login_error)): ?>
          <div class="error-message">
            <i class="bi bi-exclamation-triangle me-2"></i><?= $login_error ?>
          </div>
        <?php endif; ?>

        <form method="POST">
          <div class="mb-3">
            <label for="password" class="form-label">Admin Password</label>
            <input type="password" class="form-control" id="password" name="password" required>
          </div>
          <button type="submit" name="admin_login" class="btn-login">
            <i class="bi bi-shield-check me-2"></i>Login to Dashboard
          </button>
        </form>

        <div class="text-center mt-3">
          <a href="index.php" style="color: var(--accent-color); text-decoration: none;">
            <i class="bi bi-arrow-left me-1"></i>Back to Game Hub
          </a>
        </div>
      </div>
    </body>
    </html>
    <?php
    exit;
}

// Get game statistics
$total_games = 0;
$active_games = 0;
$total_players = 0;
$total_rounds = 0;

// Check if game tables exist
$tables_exist = true;
$table_check = $conn->query("SHOW TABLES LIKE 'game_rooms'");
if ($table_check->num_rows === 0) {
    $tables_exist = false;
} else {
    // Get game statistics
    $games_result = $conn->query("SELECT COUNT(*) as count FROM game_rooms");
    $total_games = $games_result->fetch_assoc()['count'];

    $active_result = $conn->query("SELECT COUNT(*) as count FROM game_rooms WHERE status IN ('waiting', 'playing', 'break')");
    $active_games = $active_result->fetch_assoc()['count'];

    $players_result = $conn->query("SELECT COUNT(*) as count FROM game_players");
    $total_players = $players_result->fetch_assoc()['count'];

    $rounds_result = $conn->query("SELECT COUNT(*) as count FROM game_rounds");
    $total_rounds = $rounds_result->fetch_assoc()['count'];
}

// Handle delete all games action
if (isset($_POST['delete_all_games'])) {
    if ($tables_exist) {
        $conn->query("DELETE FROM game_results");
        $conn->query("DELETE FROM game_rounds");
        $conn->query("DELETE FROM game_players");
        $conn->query("DELETE FROM game_rooms");
        header("Location: admin_game.php?deleted=1");
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Game Admin Dashboard - QuickMeet</title>
  <meta name="description" content="Game admin dashboard for QuickMeet mathematical strategy game">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">
  <link href="assets/css/main.css" rel="stylesheet">

  <style>
    body {
      background: var(--background-color);
      color: var(--default-color);
      padding: 2rem 0;
    }
    .admin-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }
    .admin-header {
      background: var(--surface-color);
      padding: 2rem;
      border-radius: 15px;
      margin-bottom: 2rem;
      text-align: center;
    }
    .admin-header h1 {
      color: var(--accent-color);
      margin-bottom: 0.5rem;
    }
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }
    .stat-card {
      background: var(--surface-color);
      padding: 2rem;
      border-radius: 15px;
      text-align: center;
      border: 1px solid rgba(206, 175, 127, 0.1);
    }
    .stat-number {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--accent-color);
      display: block;
      margin-bottom: 0.5rem;
    }
    .stat-label {
      color: var(--default-color);
      font-size: 1rem;
    }
    .admin-section {
      background: var(--surface-color);
      padding: 2rem;
      border-radius: 15px;
      margin-bottom: 2rem;
    }
    .section-title {
      color: var(--accent-color);
      margin-bottom: 1rem;
    }
    .btn-danger-custom {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
    }
    .btn-danger-custom:hover {
      background: #c82333;
      color: white;
    }
    .btn-secondary-custom {
      background: var(--accent-color);
      color: var(--contrast-color);
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
    }
    .btn-secondary-custom:hover {
      background: #b8956f;
      color: var(--contrast-color);
      text-decoration: none;
    }
    .alert-custom {
      background: rgba(40, 167, 69, 0.1);
      color: #28a745;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1rem;
    }
    .no-data {
      text-align: center;
      padding: 3rem;
      color: var(--default-color);
      opacity: 0.7;
    }
  </style>
</head>

<body>
  <div class="admin-container">
    <!-- Admin Header -->
    <div class="admin-header" data-aos="fade-up">
      <h1><i class="bi bi-controller me-2"></i>Game Admin Dashboard</h1>
      <p>Manage mathematical strategy games and monitor player activity</p>
      <div class="mt-3">
        <a href="index.php" class="btn-secondary-custom me-2">
          <i class="bi bi-house me-2"></i>Back to Game Hub
        </a>
        <a href="admin_game.php?logout=1" class="btn-danger-custom"
           onclick="return confirm('Are you sure you want to logout?')">
          <i class="bi bi-box-arrow-right me-2"></i>Logout
        </a>
      </div>
    </div>

    <?php if (isset($_GET['deleted'])): ?>
      <div class="alert-custom" data-aos="fade-up">
        <i class="bi bi-check-circle me-2"></i>All game data has been successfully deleted.
      </div>
    <?php endif; ?>

    <?php if (!$tables_exist): ?>
      <div class="admin-section" data-aos="fade-up">
        <h3 class="section-title">
          <i class="bi bi-exclamation-triangle"></i> Game Database Not Set Up
        </h3>
        <p>The game database tables are not set up yet. Please set up the game database first.</p>
        <a href="setup_game_db.php" class="btn-secondary-custom">
          <i class="bi bi-gear me-2"></i>Setup Game Database
        </a>
      </div>
    <?php else: ?>
      <!-- Game Statistics -->
      <div class="stats-grid" data-aos="fade-up">
        <div class="stat-card">
          <span class="stat-number"><?= $total_games ?></span>
          <div class="stat-label">Total Games</div>
        </div>
        <div class="stat-card">
          <span class="stat-number"><?= $active_games ?></span>
          <div class="stat-label">Active Games</div>
        </div>
        <div class="stat-card">
          <span class="stat-number"><?= $total_players ?></span>
          <div class="stat-label">Total Players</div>
        </div>
        <div class="stat-card">
          <span class="stat-number"><?= $total_rounds ?></span>
          <div class="stat-label">Rounds Played</div>
        </div>
      </div>

      <!-- Game Management -->
      <div class="admin-section" data-aos="fade-up">
        <h3 class="section-title">
          <i class="bi bi-trash3"></i> Game Data Management
        </h3>
        <p>Permanently delete all game data including rooms, players, rounds, and results.</p>
        <form method="POST" style="margin-top: 1rem;">
          <button type="submit" name="delete_all_games" class="btn-danger-custom"
                  onclick="return confirm('⚠️ This will permanently delete ALL game data. Are you absolutely sure?')">
            <i class="bi bi-trash3 me-2"></i>Delete All Game Data
          </button>
        </form>
        <p style="color: #dc3545; font-weight: 500; margin-top: 1rem; margin-bottom: 0;">
          This action cannot be undone and will remove all games, players, and statistics.
        </p>
      </div>
    <?php endif; ?>

  </div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>
  <script>
    AOS.init();
  </script>
</body>
</html>
